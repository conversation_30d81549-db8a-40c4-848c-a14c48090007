from datetime import datetime, timedelta  # 时间戳转换
from bson import ObjectId
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
import my_code.radar_code as radar_code
import logging
from shared_config import (
    get_mongo,
    get_radar_collection,
    get_scene_collection,
    get_user_collection,
)
from type import ApiResponse
from validator import *

# 配置日志
logger = logging.getLogger(__name__)

data_analysis = Blueprint("data_analysis", __name__)


# 获取雷达场景坐标
@data_analysis.route("/get_scene_coordinates", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取场景坐标")
@handle_database_exceptions
@validate_request("scene_ID")
def web_get_scene_coordinates(**kwargs) -> ApiResponse:
    """
    @api {post} /get_scene_coordinates 获取场景坐标
    @apiName GetSceneCoordinates
    @apiGroup DataAnalysis
    @apiPermission jwt_required

    @apiParam {String} scene_ID 场景ID

    @apiSuccess {String} status 请求状态(success/error)
    @apiSuccess {String} message 返回消息
    @apiSuccess {Array} data 场景坐标数据
    @apiError {String} status 错误状态
    @apiError {String} message 错误信息
    """
    scene_id = kwargs["scene_ID"]
    send_data = (
        get_scene_collection()
        .find_one({"_id": ObjectId(scene_id)})
        .field("coordinates")
        .not_empty("雷达未设置场景坐标")
        .unwrap()
    )

    return (
        jsonify(
            {"status": "success", "message": "雷达场景坐标加载成功", "data": send_data}
        ),
        200,
    )


# 获取雷达坐标
@data_analysis.route("/get_radar_host", methods=["POST"])
@jwt_required()
@handle_database_exceptions
@handle_api_exceptions(info="获取雷达坐标")
@validate_request("radar_ID")
def web_get_radar_host(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    send_data = (
        get_radar_collection()
        .find_one({"ID": radar_id})
        .field("radar_coordinates")
        .not_empty(error_message="雷达未设置坐标", status="warning")
        .unwrap()
    )

    return (
        jsonify(
            {"status": "success", "message": "雷达坐标加载成功", "data": send_data}
        ),
        200,
    )


# 获取最新形变图像URL
@data_analysis.route("/get_lastest_Img", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取最新形变图像URL")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID")
def web_lastest_Img(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_id = kwargs["mission_ID"]
    img_collection = get_collection(get_mongo(), radar_id, f"img_data_{mission_id}")
    file_path = (
        img_collection.find_one({"任务ID": int(mission_id)}, sort=[("时间戳", -1)])
        .field("road_xy")
        .not_empty()
        .unwrap()
    )
    logger.info(f"http://127.0.0.1:5000/{file_path}")
    return (
        jsonify({"status": "success", "message": f"http://127.0.0.1:5000/{file_path}"}),
        200,
    )
    # return jsonify({"status": "error", "message": "获取错误"})


# 获取最新形变图像URL
@data_analysis.route("/get_history_Img", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取历史形变图像URL")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID", "begin_time", "end_time")
def web_history_Img(**kwargs) -> ApiResponse:
    radar_id, mission_id, begin_time, end_time = (
        kwargs["radar_ID"],
        kwargs["mission_ID"],
        kwargs["begin_time"],
        kwargs["end_time"],
    )
    img_data_collection = get_collection(
        get_mongo(), radar_id, f"img_data_{mission_id}"
    )
    end_time = datetime.strptime(
        end_time, "%Y-%m-%d %H:%M"
    ).timestamp()  # 解析为 datetime 对象,后转为时间戳
    logger.info(f"begin_time:{begin_time},end_time:{end_time}")

    base_data_doc = img_data_collection.find_one(
        {"任务ID": int(mission_id), "时间戳": {"$lte": end_time + 1}},
        sort=[("时间戳", -1)],
    )
    logger.info(f"base_data_doc:{base_data_doc.unwrap()}")
    file_path = base_data_doc.field("road_xy").not_empty().unwrap()
    logger.info(f"http://127.0.0.1:5000/{file_path}")
    return (
        jsonify(
            {
                "status": "success",
                "message": {"img_url": f"http://127.0.0.1:5000/{file_path}"},
            }
        ),
        200,
    )


# 加载任务列表
@data_analysis.route("/check_all_mission", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="加载任务列表")
@handle_database_exceptions
@validate_request("radar_ID")
def web_check_all_mission(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_list_doc = get_radar_collection().find_one(
        query={"ID": radar_id}, projection={"mission_ID": 1, "_id": 0}
    )

    mission_list_doc.not_empty(
        error_message="雷达未创建任务", status="warning"
    ).unwrap()
    mission_list = []
    for each_mission_id in mission_list_doc.field("mission_ID").not_empty().unwrap():
        mission_list.append(
            {"mission_ID": each_mission_id["ID"], "name": each_mission_id["name"]}
        )
    return (
        jsonify(
            {"status": "success", "message": "成功加载任务列表", "data": mission_list}
        ),
        200,
    )


# 加载任务时间段
@data_analysis.route("/check_mission_time", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="加载任务时间段")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID")
def web_check_mission_time(**kwargs) -> ApiResponse:
    radar_id, mission_id = kwargs["radar_ID"], kwargs["mission_ID"]
    base_data_doc = get_radar_collection().find_one({"ID": radar_id})
    base_data_doc.unwrap()
    if any(
        mission.get("ID") == mission_id
        for mission in base_data_doc.field("mission_ID").not_empty().unwrap()
    ):
        img_data_collection = get_collection(
            get_mongo(), radar_id, f"img_data_{mission_id}"
        )
        result = list(
            img_data_collection.aggregate(
                [
                    {
                        "$group": {
                            "_id": None,
                            "max_value": {"$max": "$时间戳"},
                            "min_value": {"$min": "$时间戳"},
                        }
                    }
                ]
            ).unwrap()
        )
        max_time = datetime.fromtimestamp(result[0]["max_value"]).strftime(
            "%Y-%m-%d %H:%M"
        )
        min_time = datetime.fromtimestamp(result[0]["min_value"]).strftime(
            "%Y-%m-%d %H:%M"
        )
        return (
            jsonify({"status": "sucess", "max_time": max_time, "min_time": min_time}),
            200,
        )
    else:
        return (
            jsonify({"status": "error", "message": "任务不存在"}),
            404,
        )


# 获取监测区域列表
@data_analysis.route("/check_all_monitor_area", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取监测区域列表")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID")
def web_check_monitor_area_all(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_id = kwargs["mission_ID"]
    monitor_area_collection = get_collection(
        get_mongo(), radar_id, f"monitor_area_{mission_id}"
    )
    all_data = list(monitor_area_collection.find().unwrap())
    send_data = []
    for each_data in all_data:
        temp_send_data = {
            "name": each_data["标签"],
            "description": each_data["描述"],
            "westLon": each_data["westLon"],
            "southLat": each_data["southLat"],
            "eastLon": each_data["eastLon"],
            "northLat": each_data["northLat"],
        }
        send_data.append(temp_send_data)
    return (
        jsonify(
            {"status": "success", "message": "监测点列表加载成功", "data": send_data}
        ),
        200,
    )


# 添加监测区域
@data_analysis.route("/add_monitor_area", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="添加监测区域")
@handle_database_exceptions
@validate_request(
    "radar_ID",
    "mission_ID",
    "name",
    "description",
    "westLon",
    "southLat",
    "eastLon",
    "northLat",
)
def web_add_monitor_area(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_id = kwargs["mission_ID"]
    monitor_area_collection = get_collection(
        get_mongo(), radar_id, f"monitor_area_{mission_id}"
    )
    data = {
        "标签": kwargs["name"],
        "描述": kwargs["description"],
        "westLon": kwargs["westLon"],
        "southLat": kwargs["southLat"],
        "eastLon": kwargs["eastLon"],
        "northLat": kwargs["northLat"],
    }
    monitor_area_collection.insert_one(data).unwrap()
    return jsonify({"status": "success", "message": "添加成功"}), 200


# 删除监测区域
@data_analysis.route("/delete_monitor_area", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="删除监测区域")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID", "area_name")
def web_delete_monitor_area(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_id = kwargs["mission_ID"]
    area_name = kwargs["area_name"]
    monitor_area_collection = get_collection(
        get_mongo(), radar_id, f"monitor_area_{mission_id}"
    )
    result = monitor_area_collection.delete_one({"标签": area_name}).unwrap()
    if result.deleted_count == 0:
        return (
            jsonify({"status": "success", "message": "删除成功，但未找到对应数据"}),
            200,
        )
    return jsonify({"status": "success", "message": "删除成功"}), 200


# 雷达启动或停止
@data_analysis.route("/radar_control", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="雷达启动或停止")
@handle_database_exceptions
@validate_request("radar_ID", "is_work")
def web_radar_control(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    is_work = bool(int(kwargs["is_work"]))
    result = radar_code.ArcSAR_id_map[radar_id].radar_work_control(not is_work)
    if result == "success":
        return jsonify({"status": "success", "message": "雷达设置成功"}), 200
    else:
        return jsonify({"status": "error", "message": result}), 500


# 查询监测区域形变值
@data_analysis.route("/get_monitor_area_deformation", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="查询监测区域形变值")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID", "area_list", "begin_time", "end_time")
def web_get_monitor_area_deformation(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_id = kwargs["mission_ID"]
    area_list = kwargs["area_list"]
    begin_time = datetime.fromisoformat(kwargs["begin_time"].replace("Z", "+00:00"))
    end_time = datetime.fromisoformat(
        kwargs["end_time"].replace("Z", "+00:00")
    ) + timedelta(minutes=1)
    coll_deformation = get_collection(
        get_mongo(), radar_id, f"deformation_data_ {mission_id}"
    )
    deformation_data = (
        coll_deformation.find(
            {
                "area_name": {"$in": area_list},
                "timestamp": {"$gte": begin_time, "$lte": end_time},
            },
            sort=[("timestamp", 1)],
        )
        .not_empty()
        .unwrap()
    )
    send_data = []
    for each_data in deformation_data:
        temp_data = {
            "deformation": each_data["deformation"],
            "timestamp": each_data["timestamp"].strftime("%Y-%m-%d %H:%M:%S"),
            "area_name": each_data["area_name"],
        }
        send_data.append(temp_data)
    return jsonify({"status": "success", "message": "成功获取", "data": send_data}), 200
