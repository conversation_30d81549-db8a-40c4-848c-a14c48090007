{"data_mtime": 1750399112, "dep_lines": [9, 6, 7, 8, 9, 10, 11, 12, 18, 19, 20, 21, 22, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 20, 5, 10, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["my_code.radar_code", "bson", "flask", "flask_jwt_extended", "my_code", "typing", "logging", "shared_config", "type", "shutil", "os", "validator", "dotenv", "http", "builtins", "_frozen_importlib", "abc", "dotenv.main", "flask.blueprints", "flask.sansio", "flask.sansio.blueprints", "flask.sansio.scaffold", "flask.wrappers", "flask_jwt_extended.view_decorators", "validator.validator_framework", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "5126b65be1e4e390dd4c6b304e2bc62b4b878a49", "id": "my_code.web_code.radar_manage", "ignore_all": true, "interface_hash": "df96df0cbec7d4ef90fa405437f99aca349339c6", "mtime": 1750399420, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\web_code\\radar_manage.py", "plugin_data": null, "size": 8506, "suppressed": [], "version_id": "1.15.0"}