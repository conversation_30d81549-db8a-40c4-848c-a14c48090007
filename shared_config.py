"""
共享配置模块
包含数据库连接、集合对象和其他共享资源，用于解决循环导入问题
"""

import logging
from typing import Optional
from database import mongo_connector
from validator import get_collection, DatabaseHelper
from enum import StrEnum
from pymongo import MongoClient

# 配置日志
logger = logging.getLogger(__name__)

# 数据库相关常量
BASEDATA = "base_data"


class BaseData(StrEnum):
    RADAR = "radar"
    USER = "users"
    SCENE = "scene"


# 全局变量，在初始化后设置
mongo: Optional[MongoClient] = None
radar_collection: Optional[DatabaseHelper] = None
user_collection: Optional[DatabaseHelper] = None
scene_collection: Optional[DatabaseHelper] = None


def init_shared_config(app):
    """
    初始化共享配置
    在Flask应用和蓝图注册后调用
    """
    global mongo, radar_collection, user_collection, scene_collection

    # 数据库连接
    logger.info("配置数据库并连接")
    mongo_connector.init_app(app)
    mongo = mongo_connector.client
    logger.info("数据库连接成功")

    logger.info("获取数据库连接")
    radar_collection = get_collection(mongo, BASEDATA, BaseData.RADAR.value)
    user_collection = get_collection(mongo, BASEDATA, BaseData.USER.value)
    scene_collection = get_collection(mongo, BASEDATA, BaseData.SCENE.value)

    logger.info("共享配置初始化完成")


def get_mongo() -> MongoClient:
    """获取MongoDB连接"""
    if mongo is None:
        raise RuntimeError("MongoDB连接未初始化，请先调用init_shared_config")
    return mongo


def get_radar_collection() -> DatabaseHelper:
    """获取雷达集合"""
    if radar_collection is None:
        raise RuntimeError("雷达集合未初始化，请先调用init_shared_config")
    return radar_collection


def get_user_collection() -> DatabaseHelper:
    """获取用户集合"""
    if user_collection is None:
        raise RuntimeError("用户集合未初始化，请先调用init_shared_config")
    return user_collection


def get_scene_collection() -> DatabaseHelper:
    """获取场景集合"""
    if scene_collection is None:
        raise RuntimeError("场景集合未初始化，请先调用init_shared_config")
    return scene_collection
