{".class": "MypyFile", "_fullname": "my_code.web_code.users", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ApiResponse": {".class": "SymbolTableNode", "cross_ref": "type.ApiResponse", "kind": "Gdef"}, "Blueprint": {".class": "SymbolTableNode", "cross_ref": "flask.blueprints.Blueprint", "kind": "Gdef"}, "DatabaseHelper": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.DatabaseHelper", "kind": "Gdef"}, "MongoClient": {".class": "SymbolTableNode", "cross_ref": "pymongo.synchronous.mongo_client.MongoClient", "kind": "Gdef"}, "RequestValidator": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.RequestValidator", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.users.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.users.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.users.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.users.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.users.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.users.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "bcrypt": {".class": "SymbolTableNode", "cross_ref": "bcrypt", "kind": "Gdef"}, "check_password_hash": {".class": "SymbolTableNode", "cross_ref": "werkzeug.security.check_password_hash", "kind": "Gdef"}, "create_access_token": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.utils.create_access_token", "kind": "Gdef"}, "get_collection": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.get_collection", "kind": "Gdef"}, "get_jwt_identity": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.utils.get_jwt_identity", "kind": "Gdef"}, "handle_api_exceptions": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.handle_api_exceptions", "kind": "Gdef"}, "handle_database_exceptions": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.handle_database_exceptions", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "jwt_required": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.view_decorators.jwt_required", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "app.logger", "kind": "Gdef"}, "mongo": {".class": "SymbolTableNode", "cross_ref": "app.mongo", "kind": "Gdef"}, "radar_collection": {".class": "SymbolTableNode", "cross_ref": "app.radar_collection", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "scene_collection": {".class": "SymbolTableNode", "cross_ref": "app.scene_collection", "kind": "Gdef"}, "user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "my_code.web_code.users.user", "name": "user", "type": "flask.blueprints.Blueprint"}}, "user_collection": {".class": "SymbolTableNode", "cross_ref": "app.user_collection", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.validate", "kind": "Gdef"}, "validate_request": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.validate_request", "kind": "Gdef"}, "web_change_password": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.users.web_change_password", "name": "web_change_password", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_change_password", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.users.web_change_password", "name": "web_change_password", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "web_login": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.users.web_login", "name": "web_login", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_login", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.users.web_login", "name": "web_login", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "web_login", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "web_register": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.users.web_register", "name": "web_register", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_register", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.users.web_register", "name": "web_register", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "web_register", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\web_code\\users.py"}