"""
雷达信息模块
提供雷达设备的详细信息、状态监控、历史记录等功能
"""

from datetime import datetime, timedelta  # 时间戳转换
from bson import ObjectId
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
import my_code.radar_code as radar_code
from app import (
    mongo,
    radar_collection,
    scene_collection,
    user_collection,
    logger,
    ApiResponse,
)
from validator import *

# 创建蓝图
radar_information = Blueprint("radar_information", __name__)


@radar_information.route("/update_radar_information", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="更新雷达信息")
@handle_database_exceptions
@validate_request("radar_ID")
def web_update_radar_information(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    # result = radar_code.ArcSAR_id_map[radar_ID].query_radar_state()
    if radar_id in radar_code.ArcSAR_id_map:
        result = radar_code.ArcSAR_id_map[radar_id].query_radar_state()
    else:
        result = "雷达离线"

    # result = radar_code.query_radar_state(radar_ID)
    radar_information_collection = get_collection(mongo, radar_id, "radar_information")
    results_doc = (
        radar_information_collection.find({"serial_number": {"$gte": 1, "$lte": 49}})
        .not_empty()
        .unwrap()
    )
    radar_information_data = []
    for each_doc in results_doc:
        each_doc_data = {
            "ID": each_doc["serial_number"],
            "label": each_doc["mean"],
            "value": each_doc["data"],
        }
        radar_information_data.append(each_doc_data)
    if result == "success":
        return (
            jsonify(
                {
                    "states": "success",  # 状态信息
                    "message": "查询成功",  # 附加消息
                    "data": radar_information_data,  # 数据
                }
            ),
            200,
        )
    else:
        return (
            jsonify(
                {
                    "states": "warning",  # 状态信息
                    "message": "查询失败，该信息为雷达最后在线时刻的状态",  # 附加消息
                    "data": radar_information_data,  # 数据
                }
            ),
            200,
        )
