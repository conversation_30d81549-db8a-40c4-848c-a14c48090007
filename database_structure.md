# 数据库结构设计

## MongoDB数据库结构图

```mermaid
graph TD
    MongoDB[MongoDB数据库服务器] --> BaseData[base_data数据库]
    MongoDB --> WebUser[web_user数据库]
    
    BaseData --> Radar[radar集合]
    BaseData --> Users[users集合]
    BaseData --> Scene[scene集合]
    
    Radar --> RadarFields["雷达字段:
        - ID: string
        - name: string
        - is_online: number
        - is_work: number
        - mission_ID: array
        - radar_coordinates: array
        - scene: object"]
        
    Users --> UserFields["用户字段:
        - username: string
        - scene_ID: array
        - other user info"]
        
    Scene --> SceneFields["场景字段:
        - _id: ObjectId
        - name: string
        - background_pack: string
        - coordinates: array
        - radar_ID: array"]
```

```mermaid
graph TD
    RadarDB[雷达专属数据库<ID>] --> RadarInfo[radar_information集合]
    RadarDB --> SceneParam[scene_parameter集合]
    RadarDB --> PlatformCmd[platform_command集合]
    RadarDB --> ImgData["img_data_<mission_ID>集合"]
    RadarDB --> DeformData["deformation_data_<mission_ID>集合"]
    RadarDB --> ConfidenceData["confidence_data_<mission_ID>集合"]
    RadarDB --> MoveTargetData["move_target_data_<mission_ID>集合"]
    
    RadarInfo --> RadarInfoFields["雷达信息字段:
        - start_byte_position: number
        - all_byte: number
        - data_type: string
        - data: various types"]
        
    SceneParam --> SceneParamFields["场景参数字段:
        - code_name: string
        - start_byte_position: number
        - all_byte: number
        - data_type: string
        - data: various types"]
        
    ImgData --> ImgDataFields["图像数据字段:
        - 扫描序号: number
        - 时间戳: number
        - 任务ID: number
        - 距离像素间隔: number
        - 距离像素点数量: number
        - 最小距离: number
        - 角度像素间隔: number
        - 角度像素点数量: number
        - 最小角度: number
        - 数据类型: number
        - 最大幅值: number
        - road_polar: string
        - road_cart: string"]
```
## 注意事项

1. 每个雷达都有自己独立的数据库，数据库名为雷达ID
2. 任务相关的集合（图像、形变、置信度数据等）都包含任务ID
3. 数据类型包括：
   - charchar: 字符数组
   - char: 单个字符
   - float32: 32位浮点数
   - float64: 64位浮点数
   - int16: 16位整数
   - int32: 32位整数
4. 文件路径格式：`<base_path>/<radar_id>/work_data/<mission_id>/<data_type>`

## 数据库说明

### base_data数据库
主数据库，包含系统的基础信息：
- radar集合：存储所有雷达的基本信息
- users集合：存储用户信息和权限
- scene集合：存储场景配置信息

### web_user数据库
用于Web应用的用户认证和会话管理

### 雷达专属数据库
每个雷达都有独立的数据库，存储该雷达的：
- 雷达信息（radar_information）
- 场景参数（scene_parameter）
- 平台命令（platform_command）
- 任务数据（图像、形变、置信度等）

## 数据关系
1. 用户 -> 场景：多对多关系（通过scene_ID数组）
2. 场景 -> 雷达：多对多关系（通过radar_ID数组）
3. 雷达 -> 任务：一对多关系（通过mission_ID数组）