import socket
import select
import struct
from collections import namedtuple
from logging import getLogger, basicConfig, INFO
from enum import Enum
from pymongo import MongoClient
import pandas as pd
import os
from typing import (
    Optional,
    Tuple,
    cast,
    Dict,
    List,
    Any,
    Union,
    Optional,
    Callable,
    Iterable,
    Generator,
)
import hashlib
import snappy  # type: ignore
import numpy as np
from tifffile import imwrite, imread  # type: ignore
import time
import multiprocessing as mp
from scipy.interpolate import griddata  # type: ignore
import matplotlib.pyplot as plt

# --- 配置日志，方便调试 ---
basicConfig(level=INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = getLogger(__name__)

BASE_FILE_PATH = ""
# HOST: str = "*************"
HOST: str = "127.0.0.1"
PORT: int = 1030

client: MongoClient = MongoClient("mongodb://localhost:27017/")
db_base_data = client["base_data"]
radar_collection = db_base_data["radar"]


class CommandHeader(Enum):
    PLATFORM = 0x5A5A
    RADAR = 0x3C3C


Radar = namedtuple("Radar", ["Id_str", "Socket"])
radar_list: list[Radar] = []
ArcSAR_map: dict[socket.socket, "ArcSAR"] = {}
ArcSAR_id_map: dict[str, "ArcSAR"] = {}


class ArcSAR:
    """
    ArcSAR类：管理与单个雷达设备的通信和数据处理

    此类封装了与雷达设备通信的所有功能，包括：
    - 雷达状态查询和控制
    - 数据接收和解析
    - 图像数据处理
    - 数据库操作
    """

    HEADER_FORMAT = "<H I B B B I B 10x I"
    HEADER_SIZE = struct.calcsize(HEADER_FORMAT)

    def __init__(self, client_socket: socket.socket, address: tuple[str, int]):
        self.id: str = ""  # 雷达ID，首次通信时设置
        self.socket: socket.socket = client_socket  # 与雷达设备的TCP连接
        self.counter: int = 0  # 用于数据包的序号
        self.address: tuple[str, int] = address  # 雷达设备的IP地址和端口
        self.payload_data: bytes = b""  # 数据

        self.socket.settimeout(5)

    def radar_report(self) -> bool:
        """
        解析雷达上报的指令并处理

        此方法接收雷达发送的数据，解析指令头部，并根据扩展码调用相应的处理函数。
        处理函数包括：
        - 00: 更新雷达基本信息
        - 01: 上传图像数据
        - 02/03: 上传形变或置信度数据
        - 04: 上传移动目标数据
        - 05: 添加日志文件
        - 06: 更新雷达时间

        Returns:
            bytes: 处理结果，成功返回b'success'，失败返回b''
        """
        # 1. 接收完整数据包
        received_package = self.recv_data(self.socket)

        # 2. 检查接收结果，如果为None，说明连接已断开
        if received_package is None:
            logger.info(f"来自{self.address}的连接已断开")
            return False

        # 3. 解包数据
        header_data, payload_data = received_package
        self.payload_data = payload_data if payload_data else b""

        # 4. 解析头部
        try:
            (head, radar_id, command, extend, sign, con_int, status_code, data_len) = (
                struct.unpack(self.HEADER_FORMAT, header_data)
            )
        except struct.error as e:
            logger.error(f"无法从{self.address}处正确解包头部: {e}")
            return False  # 头部格式错误，可能是无效数据

        # 如果是第一次通信，设置雷达ID
        """
        >>> a = struct.unpack("<I", b'\x00\x10\x05\xad')[0]
        >>> f"{a:08X}"
        'AD051000'
        """
        if not self.id:
            self.id = f"{radar_id:08X}"  # 使用8位大写十六进制作为ID
            logger.info(f"来自{self.address}的雷达的设备ID为: {self.id}")
            ArcSAR_id_map[self.id] = self
            self.register_or_update_online_status()

        # 5. 根据扩展码(extend)分派任务
        logger.info(
            f"设备ID为{self.id}的雷达发送了数据，命令码=0x{command:02x}, 扩展码=0x{extend:02x}, 主要数据长度={data_len}"
        )

        # 处理 命令码 为 0x40
        if (
            head == CommandHeader.RADAR.value
            and command == 0x40
            and extend in range(0x00, 0x06 + 0x01)
        ):
            # 使用字典进行分派，更优雅
            dispatch_map = {
                0x00: self.update_radar_imformation,
                0x01: self.upload_image_data,
                0x02: self.upload_defo_or_confi_data,
                0x03: self.upload_defo_or_confi_data,
                0x04: self.upload_moving_target_data,
                0x05: self.upload_log_file,
                0x06: self.update_radar_time,
            }
            logger.info(f"分配处理函数为{dispatch_map[extend].__name__}")
            respond_data_bytes = dispatch_map[extend]()
            respond_data_len = len(respond_data_bytes) if respond_data_bytes else 0

            if respond_data_len > 0:
                data = struct.pack(
                    "{ArcSAR.HEADER_FORMAT} {return_data_len}s",
                    CommandHeader.RADAR.value,
                    radar_id,
                    command,
                    extend,
                    0x01,
                    con_int,
                    0x00,  # 状态码，设置正常
                    respond_data_len,
                )
            elif respond_data_len == 0:
                data = struct.pack(
                    ArcSAR.HEADER_FORMAT,
                    CommandHeader.RADAR.value,
                    radar_id,
                    command,
                    extend,
                    0x01,
                    con_int,
                    0x00,  # 状态码，设置正常
                    respond_data_len,
                )
            else:
                logger.error(
                    f"从设备ID为{self.id}的雷达解包得到命令码0x{command:02x}, 扩展码0x{extend:02x}，但响应数据长度为{respond_data_len}，不符合协议"
                )
                return False

            self.socket.sendall(data)
            logger.info(
                f"成功向ID为{self.id}的雷达发送响应数据，长度为{respond_data_len}，内容为{respond_data_bytes.hex(' ') if respond_data_bytes else '空'}"
            )
        else:
            logger.warning(
                f"从设备ID为{self.id}的雷达解包得到命令码0x{command:02x}, 扩展码0x{extend:02x}不符合主动上报格式"
            )
            return False
        return True

    def recv_data(
        self, socket: socket.socket, default_chunk_size: int = 1024
    ) -> Optional[Tuple[bytes, Optional[bytes]]]:
        """
        通过循环接收数据块(chunk)的方式，从 socket 中准确接收一个完整的消息。

        Args:
            sock (socket.socket): 一个已连接的 socket 对象。
            chunk_size (int): 每次从 socket 缓冲区读取的最大字节数。

        Returns:
            bytes: 接收到的完整消息字节流，如果连接中断则返回 None。
        """
        # 接受头部
        header_buffer = bytearray()
        logger.info(f"正从{self.address}处接受头部数据，目标长度为{ArcSAR.HEADER_SIZE}")
        while len(header_buffer) < ArcSAR.HEADER_SIZE:
            remaining = ArcSAR.HEADER_SIZE - len(header_buffer)  # 剩余需要接收的字节数
            try:
                chunk = socket.recv(min(remaining, default_chunk_size))
            except (ConnectionResetError, BrokenPipeError):
                chunk = b""  # 对待连接错误如同连接正常关闭
            if not chunk:
                return None
            header_buffer.extend(chunk)  # 将接收到的数据追加到缓冲区

        logger.info(f"成功在{self.address}接收到头部数据，长度为{len(header_buffer)}")
        header_data = bytes(header_buffer)  # 将缓冲区转换为字节流
        logger.info(f"头部数据为{ArcSAR.format_hex_pretty(header_data)}")

        # 解析数据长度
        try:
            *_, data_len = struct.unpack(self.HEADER_FORMAT, header_data)
            logger.info(f"从{self.address}解析得到的数据长度为{data_len}")
        except struct.error:
            logger.error(f"从{self.address}得到的头部是无效的，无法解析数据长度")
            return None  # 无法解析头部，放弃此包

        # 接受剩下的数据
        payload_buffer = bytearray()
        logger.info(f"开始从{self.address}处接受数据，目标长度为{data_len}")
        while len(payload_buffer) < data_len:
            remaining = data_len - len(payload_buffer)  # 剩余需要接收的字节数
            chunk = socket.recv(min(remaining, default_chunk_size))
            if not chunk:
                return None
            payload_buffer.extend(chunk)  # 将接收到的数据追加到缓冲区

        logger.info(f"成功在{self.address}接收到主要数据，长度为{len(payload_buffer)}")
        payload_data = bytes(payload_buffer)  # 将缓冲区转换为字节流
        logger.info(f"主要数据为{ArcSAR.format_hex_pretty(payload_data)}")

        return header_data, payload_data

    @staticmethod
    def format_hex_pretty(
        data: bytes, line_length: int = 28, indent: str = "  "
    ) -> str:
        """
        将字节串格式化为美观的十六进制字符串，带换行和缩进。

        Args:
            data (bytes): 需要格式化的字节数据。
            line_length (int): 每行显示的字节数。
            indent (str): 每行前面的缩进字符串。

        Returns:
            str: 格式化后的多行字符串。
        """
        if not data:
            return "<empty>"

        lines = []
        for i in range(0, len(data), line_length):
            # 1. 切出当前行的数据块
            chunk = data[i : i + line_length]

            # 2. 将数据块转换为带空格的十六进制字符串
            hex_part = chunk.hex(" ").upper()

            # 3. (可选) 添加ASCII字符表示，方便对照
            # ascii_part = "".join(chr(b) if 32 <= b < 127 else "." for b in chunk)
            # lines.append(
            #     f"{indent}{i:08x}:  {hex_part:<{line_length * 3 - 1}} |{ascii_part}|"
            # )

            # 4. 格式化当前行，使其对齐
            #    - {i:08x}: 当前行的起始偏移量，用8位十六进制表示
            #    - {hex_part:<{line_length * 3 - 1}}: 左对齐的十六进制部分。
            #      每个字节占3个字符（2个hex + 1个空格），最后减1去掉末尾空格。
            #    - {ascii_part}: ASCII部分
            lines.append(f"{indent}{i:08x}:  {hex_part:<{line_length * 3 - 1}}")

        # 在最前面加上换行符，使其在日志中与上一行分开，看起来更清晰
        return "\n" + "\n".join(lines)

    def register_or_update_online_status(self) -> bool:
        """
        处理雷达首次连接的初始化工作

        此方法执行以下操作：
        1. 检查雷达是否已在数据库中注册
        2. 如果是新雷达：
           - 在数据库中创建雷达基本信息
           - 创建必要的文件目录结构
           - 从Excel导入雷达配置信息到MongoDB
        3. 如果是已知雷达：
           - 更新在线状态

        Returns:
            str: 'successfully!' 表示初始化成功

        Raises:
            OSError: 创建目录失败时抛出
            pd.errors.EmptyDataError: Excel文件为空时抛出
            pymongo.errors.PyMongoError: MongoDB操作失败时抛出
        """
        try:  # 一个全新的雷达连接到服务器
            radar_doc = radar_collection.find_one({"ID": self.id})
            if not radar_doc:
                # 初始化 radar_collection 中的 "ID" 对应的文档
                logger.info(f"设备ID为{self.id}的雷达是新雷达，开始初始化对应的数据库")

                radar_collection.insert_one(
                    {
                        "ID": self.id,
                        "name": f"Radar{self.id}",
                        "is_online": 1,
                        "is_work": 0,
                        "mission_ID": [
                            {
                                "ID": None,
                                "name": None,
                                "coordinates": None,
                                "radar_zero_theta": None,
                            }
                        ],
                        "radar_coordinates": [],
                        "scene": None,
                    }
                )

                # 创建文件目录结构 不知道在干嘛
                radar_file_path = os.path.join(BASE_FILE_PATH, self.id)
                for subdir in ["algorithm_file", "log_file", "work_data"]:
                    os.makedirs(os.path.join(radar_file_path, subdir), exist_ok=True)
                logger.info(f"为设备ID为{self.id}的雷达创建了必要的文件目录结构")

                # 根据 id 创建数据库
                db_this_radar = client[self.id]

                # 从Excel导入配置数据
                excel_sheets = {
                    "雷达信息": "radar_information",
                    "场景参数": "scene_parameter",
                    "平台命令": "platform_command",
                }

                for sheet_name, collection_name in excel_sheets.items():
                    df = pd.read_excel("my_code/data.xlsx", sheet_name=sheet_name)
                    logger.debug(f"成功从Excel读取了{sheet_name}的数据")
                    data = df.to_dict(orient="records")
                    logger.debug(f"成功将{sheet_name}的数据转换为字典\n{data}")
                    db_this_radar[collection_name].insert_many(data)

                logger.info(f"成功为设备ID为{self.id}的雷达创建了数据库")

                return True
            else:  # 一个已经注册过的雷达重新连接
                # 更新雷达在线状态
                logger.info(f"设备ID为{self.id}的雷达已经在数据库中注册")
                radar_collection.update_one({"ID": self.id}, {"$set": {"is_online": 1}})
                logger.info(f"设备ID为{self.id}的雷达重新上线，更新在线状态为1")
                return True
        except OSError as e:
            logger.error(f"创建文件目录结构失败: {e}")
            raise
        except pd.errors.EmptyDataError as e:
            logger.error(f"从Excel导入配置数据失败: {e}")
            raise
        except Exception as e:
            logger.error(f"未知错误: {e}")
            raise

    def update_radar_imformation(self):
        """
        更新雷达基本信息

        此方法从接收到的数据中解析雷达信息并更新到数据库。支持的数据类型包括：
        - charchar: 字符数组，每个字符用点号分隔
        - char: 单个字符
        - float32: 32位浮点数
        - float64: 64位浮点数
        - int16: 16位整数
        - int32: 32位整数

        Returns:
            str: 空字符串表示成功

        Raises:
            struct.error: 数据解包错误时抛出
            pymongo.errors.PyMongoError: 数据库操作错误时抛出
        """
        try:
            logger.info(f"开始解析雷达{self.id}的基本信息")
            db_this_radar = client[self.id]

            radar_information_collection = db_this_radar["radar_information"]

            # 数据类型到解析函数的映射
            type_parsers = {
                "charchar": lambda pos, size: f"v.{'.'.join(str(n) for n in struct.unpack('<' + 'B' * size, self.payload_data[pos:pos + size]))}",
                "char": lambda pos, size: chr(
                    struct.unpack("<B", self.payload_data[pos : pos + size])[0]
                ),
                "float32": lambda pos, size: struct.unpack(
                    "<f", self.payload_data[pos : pos + size]
                )[0],
                "float64": lambda pos, size: struct.unpack(
                    "<d", self.payload_data[pos : pos + size]
                )[0],
                "int16": lambda pos, size: struct.unpack(
                    "<h", self.payload_data[pos : pos + size]
                )[0],
                "int32": lambda pos, size: struct.unpack(
                    "<I", self.payload_data[pos : pos + size]
                )[0],
            }

            # 处理每个雷达信息文档
            logger.info(f"开始更新雷达{self.id}数据库中的基本信息")
            for doc in radar_information_collection.find():
                start_pos: int = doc.get("start_byte_position")
                byte_size: int = doc.get("all_byte")
                data_type: str = doc.get("data_type")

                if data_type not in type_parsers:
                    logger.warning(f"未知数据类型 {data_type}")
                    continue

                try:
                    # 解析数据
                    parsed_data = type_parsers[data_type](start_pos, byte_size)

                    # 更新数据库
                    radar_information_collection.update_one(
                        {"start_byte_position": start_pos},
                        {"$set": {"data": parsed_data}},
                    )
                except struct.error as e:
                    logger.error(
                        f"数据解析错误，从负载数据的第{start_pos}位开始发生位置: {str(e)}"
                    )
                    continue
            logger.info(f"成功更新雷达{self.id}数据库中的基本信息")
        except Exception as e:
            logger.error(f"无法解析雷达信息: {e}")
            raise

    def upload_image_data(self):
        """
        处理雷达上传的图像数据并保存到数据库和文件系统

        此方法执行以下操作：
        1. 解析接收到的二进制数据，包括序列号、MD5校验、时间戳、任务ID等元数据
        2. 对图像数据进行MD5校验
        3. 解压缩图像数据并转换为幅度和相位两个numpy数组
        4. 将图像数据保存为TIFF格式，包含元数据
        5. 将图像信息存入MongoDB数据库

        Returns:
            str: 空字符串表示成功

        Raises:
            struct.error: 数据解包错误时抛出
            snappy.UncompressError: 数据解压缩错误时抛出
            OSError: 文件写入错误时抛出
            pymongo.errors.PyMongoError: 数据库操作错误时抛出
        """
        # 解析头部数据
        logger.info(f"开始解析雷达{self.id}上传的散射图像数据")
        meta_data_format = "< I 16s 2I fIf fIf IIf"
        meta_data_size = struct.calcsize(meta_data_format)
        try:
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                sca_im_size,
                img_max_amp,
            ) = struct.unpack(meta_data_format, self.payload_data[:meta_data_size])
        except struct.error as e:
            logger.error(f"散射图像数据头部解析错误: {str(e)}")
            return

        # 校验MD5
        logger.info(f"开始校验雷达{self.id}上传的散射图像数据MD5")
        md5_check_pos = struct.calcsize("<I 16s")
        md5_calculated = hashlib.md5(self.payload_data[md5_check_pos:]).hexdigest()
        if md5 != md5_calculated:
            logger.error(
                f"散射图像数据MD5校验失败，实际值为{md5}，计算值为{md5_calculated}"
            )
            return

        # 解压缩和处理图像数据
        try:
            logger.info(f"开始解压雷达{self.id}上传的散射图像数据")
            M, N = rng_num, ang_num
            img_data_bytes = cast(
                bytes, snappy.uncompress(self.payload_data[meta_data_size:])
            )

            magnitude_data = (
                np.frombuffer(img_data_bytes[: 2 * M * N], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )

            phase_data = (
                np.frombuffer(img_data_bytes[2 * M * N :], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )
            logger.info(f"成功解压雷达{self.id}上传的散射图像数据")
        except Exception as e:
            logger.error(f"解压缩图像数据失败: {str(e)}")
            return

        # 准备文件路径和数据库
        base_path = os.path.join(
            BASE_FILE_PATH, self.id, "work_data", str(mission_id), "image_data"
        )

        file_paths = {
            "polar": os.path.join(base_path, f"polar_{seq}.tiff"),
            "polar_phase": os.path.join(base_path, f"phase_{seq}.tiff"),
            "polar_magntiude": os.path.join(base_path, f"magnitude_{seq}.tiff"),
            "cart": os.path.join(base_path, f"cart_{seq}.png"),
        }
        logger.info(f"成功创建雷达{self.id}上传的散射图像数据文件路径: {file_paths}")

        # 保存图像数据
        logger.info(f"开始保存雷达{self.id}上传的散射图像数据至文件系统")
        radar_doc = cast(Dict[str, Any], radar_collection.find_one({"ID": self.id}))
        mission_list = radar_doc.get("mission_ID", [])
        mission_details = next(
            (m for m in mission_list if m["ID"] == str(mission_id)), None
        )

        meta2save = {
            "description": "雷达散射图像数据，第一张为幅度图，第二张为相位图",
            "theta0": ang_min,
            "dtheta": ang_res,
            "r0": rng_min,
            "dr": rng_res,
            "coordinates": (
                mission_details.get("coordinates", []) if mission_details else []
            ),
            "radar_zero_theta": (
                mission_details.get("radar_zero_theta", 0) if mission_details else 0
            ),
        }

        # 保存原始极坐标图像
        imwrite(
            file_paths["polar"],
            np.stack([magnitude_data, phase_data], axis=0),
            metadata=meta2save,
        )
        # 分别保存极坐标图像的幅度和相位
        imwrite(
            file_paths["polar_phase"],
            phase_data,
            metadata=meta2save,
        )
        imwrite(
            file_paths["polar_magntiude"],
            magnitude_data,
            metadata=meta2save,
        )
        logger.info(f"成功保存雷达{self.id}上传的散射图像数据至文件系统")

        # 把极坐标图像转换为笛卡尔坐标
        mp.Process(
            target=ArcSAR.polar2cart,
            args=(
                magnitude_data,
                rng_min,
                rng_res,
                rng_num,
                ang_min,
                ang_res,
                ang_num,
                file_paths["cart"],
                meta2save,
            ),
        ).start()

        # 保存到数据库，图片的元数据，图片的本地路径
        logger.info(f"开始保存雷达{self.id}上传的散射图像数据至数据库")
        db_this_radar = client[self.id]
        img_collection = db_this_radar[f"img_data_{mission_id}"]

        img_doc = {
            "扫描序号": seq,
            "时间戳": time_stamp,
            "任务ID": mission_id,
            "距离像素间隔": rng_res,
            "距离像素点数量": rng_num,
            "最小距离": rng_min,
            "角度像素间隔": ang_res,
            "角度像素点数量": ang_num,
            "最小角度": ang_min,
            "数据类型": data_type,
            "最大幅值": img_max_amp,
            "road_polar": file_paths["polar"],
            "road_cart": file_paths["cart"],
            "road_polar_phase": file_paths["polar_phase"],
            "road_polar_magntiude": file_paths["polar_magntiude"],
        }
        img_collection.insert_one(img_doc)
        logger.info(f"成功保存雷达{self.id}上传的散射图像数据至数据库")
        return

    def upload_defo_or_confi_data(self):
        # 解析头部数据
        logger.info(f"开始解析雷达{self.id}上传的(形变 | 置信度)图像数据")
        meta_data_format = "< I 16s 2I fIf fIf II"
        meta_data_size = struct.calcsize(meta_data_format)
        try:
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                im_size,
            ) = struct.unpack(meta_data_format, self.payload_data[:meta_data_size])
        except struct.error as e:
            logger.error(f"(形变 | 置信度)图像数据头部解析错误: {str(e)}")
            return

        if data_type == 20:
            img_type_en, img_type_cn = "deformation", "形变"
            logger.info(f"雷达{self.id}上传的图像类型为：形变图像")
        elif data_type == 30:
            img_type_en, img_type_cn = "Confidence", "置信度"
            logger.info(f"雷达{self.id}上传的图像类型为：置信度图像")
        else:
            logger.error(f"未知的数据类型: {data_type}")
            return

        # 校验MD5
        logger.info(f"开始校验雷达{self.id}上传的{img_type_cn}图像数据MD5")
        md5_check_pos = struct.calcsize("<I 16s")
        md5_calculated = hashlib.md5(self.payload_data[md5_check_pos:]).hexdigest()
        if md5 != md5_calculated:
            logger.error(
                f"{img_type_cn}图像数据MD5校验失败，实际值为{md5}，计算值为{md5_calculated}"
            )
            return

        # 解压缩和处理图像数据
        try:
            logger.info(f"开始解压雷达{self.id}上传的{img_type_cn}图像数据")
            M, N = rng_num, ang_num
            img_data_bytes = cast(
                bytes, snappy.uncompress(self.payload_data[meta_data_size:])
            )

            img_data = (
                np.frombuffer(img_data_bytes[: 2 * M * N], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )
            logger.info(f"成功解压雷达{self.id}上传的{img_type_cn}图像数据")
        except Exception as e:
            logger.error(f"解压缩图像数据失败: {str(e)}")
            return

        # 准备文件路径和数据库
        base_path = os.path.join(
            BASE_FILE_PATH, self.id, "work_data", str(mission_id), f"{img_type_en}_data"
        )

        file_path = os.path.join(base_path, f"{img_type_en}_img_{seq}.tiff")

        logger.info(
            f"成功创建雷达{self.id}上传的{img_type_cn}图像数据文件路径: {file_path}"
        )

        # 保存图像数据
        logger.info(f"开始保存雷达{self.id}上传的{img_type_cn}图像数据至文件系统")
        radar_doc = cast(Dict[str, Any], radar_collection.find_one({"ID": self.id}))
        mission_list = radar_doc.get("mission_ID", [])
        mission_details = next(
            (m for m in mission_list if m["ID"] == str(mission_id)), None
        )

        meta2save = {
            "description": "雷达散射图像数据，第一张为幅度图，第二张为相位图",
            "theta0": ang_min,
            "dtheta": ang_res,
            "r0": rng_min,
            "dr": rng_res,
            "coordinates": (
                mission_details.get("coordinates", []) if mission_details else []
            ),
            "radar_zero_theta": (
                mission_details.get("radar_zero_theta", 0) if mission_details else 0
            ),
        }

        # 保存原始极坐标图像
        imwrite(
            file_path,
            img_data,
            metadata=meta2save,
        )
        logger.info(f"成功保存雷达{self.id}上传的{img_type_cn}图像数据至文件系统")

        # 保存到数据库，图片的元数据，图片的本地路径
        logger.info(f"开始保存雷达{self.id}上传的{img_type_cn}图像数据至数据库")
        db_this_radar = client[self.id]
        img_collection = db_this_radar[f"{img_type_en.lower()}_data_{mission_id}"]

        img_doc = {
            "扫描序号": seq,
            "时间戳": time_stamp,
            "任务ID": mission_id,
            "距离像素间隔": rng_res,
            "距离像素点数量": rng_num,
            "最小距离": rng_min,
            "角度像素间隔": ang_res,
            "角度像素点数量": ang_num,
            "最小角度": ang_min,
            "数据类型": data_type,
            "图像大小": im_size,
            "road_path": file_path,
        }
        img_collection.insert_one(img_doc)
        logger.info(f"成功保存雷达{self.id}上传的{img_type_cn}图像数据至数据库")
        return

    def upload_moving_target_data(self):
        # 解析头部数据
        logger.info(f"开始解析雷达{self.id}上传的动目标数据")
        meta_data_format = "< I 16s 2I fIf fIf III"
        meta_data_size = struct.calcsize(meta_data_format)
        try:
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                mt_num,
                mt_data_size,
            ) = struct.unpack(meta_data_format, self.payload_data[:meta_data_size])
        except struct.error as e:
            logger.error(f"动目标图像数据头部解析错误: {str(e)}")
            return

        # 校验MD5
        logger.info(f"开始校验雷达{self.id}上传的动目标数据MD5")
        md5_check_pos = struct.calcsize("<I 16s")
        md5_calculated = hashlib.md5(self.payload_data[md5_check_pos:]).hexdigest()
        if md5 != md5_calculated:
            logger.error(
                f"动目标数据MD5校验失败，实际值为{md5}，计算值为{md5_calculated}"
            )
            return

        # 获取动目标数据
        mt_data_bytes = self.payload_data[
            meta_data_size : meta_data_size + mt_data_size
        ]
        all_target = []
        for i in range(mt_num):
            target_format = "< 4f"
            target_size = struct.calcsize(target_format)
            target_data = struct.unpack(
                target_format, mt_data_bytes[i * target_size : (i + 1) * target_size]
            )
            all_target.append(
                {
                    "目标编号": target_data[0],
                    "目标角度": target_data[1],
                    "目标距离": target_data[2],
                    "目标速度": target_data[3],
                }
            )
        logger.info(f"成功解析雷达{self.id}上传的动目标数据")

        # 保存到数据库
        logger.info(f"开始保存雷达{self.id}上传的动目标数据至数据库")
        db_this_radar = client[self.id]

        img_doc = {
            "扫描序号": seq,
            "时间戳": time_stamp,
            "任务ID": mission_id,
            "距离像素间隔": rng_res,
            "距离像素点数量": rng_num,
            "最小距离": rng_min,
            "角度像素间隔": ang_res,
            "角度像素点数量": ang_num,
            "最小角度": ang_min,
            "数据类型": data_type,
            "动目标个数": mt_num,
        }
        img_doc["目标信息"] = all_target
        mt_collection = db_this_radar[f"move_target_data_{mission_id}"]
        mt_collection.insert_one(img_doc)
        logger.info(f"成功保存雷达{self.id}上传的动目标数据至数据库")
        return

    def upload_log_file(self):
        name_str = self.payload_data[:128].decode("ascii").split(",")[0]
        log_road = BASE_FILE_PATH + self.id + "/log_file/"
        logger.info(f"开始保存雷达{self.id}上传的日志数据至文件系统")
        path = os.path.join(log_road, name_str)
        logger.info(f"日志文件保存路径为{path}")
        with open(path, "ab") as f:
            f.write(self.payload_data[128:])
            print("接收log文件成功")
        return

    def update_radar_time(self):
        time_stamp = int(time.time())
        sendback_data = struct.pack("<I", time_stamp)
        logger.info(f"成功更新雷达{self.id}的时间戳为{time_stamp}")
        return sendback_data

    @staticmethod
    def polar2cart(
        img,
        rng_min,
        rng_res,
        rng_num,
        ang_min,
        ang_res,
        ang_num,
        file_path,
    ):
        ang_values = np.linspace(ang_min, ang_min + ang_res * (ang_num - 1), ang_num)
        rng_values = np.linspace(rng_min, rng_min + rng_res * (rng_num - 1), rng_num)

        rr, aa = np.meshgrid(rng_values, ang_values)
        x = rr * np.cos(aa)
        y = rr * np.sin(aa)

        x_cart = np.linspace(x.min(), x.max(), rng_num)
        y_cart = np.linspace(y.min(), y.max(), ang_num)
        xx_new, yy_new = np.meshgrid(x_cart, y_cart)

        # 3. 插值计算
        # 将原始(x, y)坐标点展平，以符合griddata的输入格式 (N, 2)
        source_points = np.vstack((x.ravel(), y.ravel())).T

        # 将幅度数据插值到新的笛卡尔网格上
        img = griddata(
            points=source_points,
            values=img.ravel(),
            xi=(xx_new, yy_new),
            method="linear",  # 使用线性插值，'nearest'更快但效果粗糙，'cubic'更平滑但计算慢
            fill_value=0,  # 如果插值点没有对应的数据，则返回0
        )

        # 保存到文件
        img = 20 * np.log10(
            np.where(
                img == 0,  # 条件：如果值等于0
                np.min(img[img != 0]),  # 则替换为：数组中最小的非零值
                img,  # 否则：保持原值
            )
            / np.max(img)
        )

        plt.axis("off")
        min_val = np.min(img)
        alpha = np.ones_like(img)
        alpha[img == min_val] = 0
        plt.imshow(img, cmap="jet", aspect="auto", alpha=alpha)
        plt.savefig(file_path, bbox_inches="tight", pad_inches=0)

    def query_radar_state(self):
        logger.info(f"开始查询雷达{self.id}的状态")
        self.counter += 1
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM,
            self.id,
            0x02,
            0x00,
            0x00,
            self.counter,
            0x00,
            0,
        )
        self.socket.sendall(send_code)
        logger.info(f"成功向雷达{self.id}发送状态查询指令")

        recdata = self.recv_data(self.socket)
        if not isinstance(recdata, tuple):
            logger.error(f"接收数据错误: {recdata}")
            return
        query_res_header = struct.unpack(ArcSAR.HEADER_FORMAT, recdata[0])
        head, radar_id, command, extend, sign, con_int, status_code, data_len = (
            query_res_header
        )
        if head != CommandHeader.PLATFORM.value:
            logger.error(f"查询结果的设备类型错误: {head}")
            return
        if radar_id != self.id:
            logger.error(f"查询结果的设备ID错误: {radar_id}")
            return
        if command != 0x02:
            logger.error(f"查询结果的命令码错误: {command}")
            return
        if extend != 0x00:
            logger.error(f"查询结果的扩展码错误: {extend}")
            return
        if sign != 0x00:
            logger.error(f"查询结果非响应类型: {sign}")
            return
        if con_int != self.counter:
            logger.error(
                f"查询结果的计数器错误，期望值为{self.counter}，实际值为{con_int}"
            )
            return

        status = {
            0x00: "设置正确",
            0x01: "参数错误",
            0x02: "数据长度错误",
            0x03: "重发",
            0x04: "雷达正在加载参数,暂时不能开始工作",
            0x05: "处于工作状态,设置失败",
        }.get(status_code, "未知错误")
        if status == "设置正确":
            logger.info(f"雷达{self.id}状态查询成功，状态为{status}")
        else:
            logger.error(f"雷达{self.id}状态查询失败，状态为{status}")
            return

    # 雷达控制，发送指令
    # extend_code
    # 0：开始工作
    # 1：停止工作
    # 2：重启雷达
    def radar_work_control(self, extend_code) -> str:
        work_status = {
            0x00: "开始工作",
            0x01: "停止工作",
            0x02: "重启雷达",
        }.get(extend_code, "扩展码错误")
        logger.info(f"开始向雷达{self.id}发送工作控制指令:{work_status}")
        if extend_code not in [0x00, 0x01, 0x02]:
            logger.error(f"工作控制指令错误: {work_status}")
            return "工作控制指令错误"

        self.counter += 1

        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM,
            self.id,
            0x03,
            extend_code,
            0x00,
            self.counter,
            0x00,
            0,
        )
        self.socket.sendall(send_code)
        logger.info(f"成功向雷达{self.id}发送工作控制指令:{work_status}")

        recdata = self.recv_data(self.socket)
        if not isinstance(recdata, tuple):
            logger.error(f"接收数据错误: {recdata}")
            return "接收数据错误"
        query_res_header = struct.unpack(ArcSAR.HEADER_FORMAT, recdata[0])
        head, radar_id, command, extend, sign, con_int, status_code, data_len = (
            query_res_header
        )
        if head != CommandHeader.PLATFORM.value:
            logger.error(f"工作控制指令响应结果的设备类型错误: {head}")
            return "工作控制指令响应结果的设备类型错误"
        if radar_id != self.id:
            logger.error(f"工作控制指令响应结果的设备ID错误: {radar_id}")
            return "工作控制指令响应结果的设备ID错误"
        if command != 0x02:
            logger.error(f"工作控制指令响应结果的命令码错误: {command}")
            return "工作控制指令响应结果的命令码错误"
        if extend != 0x00:
            logger.error(f"工作控制指令响应结果的扩展码错误: {extend}")
            return "工作控制指令响应结果的扩展码错误"
        if sign != 0x00:
            logger.error(f"工作控制指令响应结果非响应类型: {sign}")
            return "工作控制指令响应结果非响应类型"
        if con_int != self.counter:
            logger.error(
                f"工作控制指令响应结果的计数器错误，期望值为{self.counter}，实际值为{con_int}"
            )
            return "工作控制指令响应结果的计数器错误"

        status = {
            0x00: "设置正确",
            0x01: "参数错误",
            0x02: "数据长度错误",
            0x03: "重发",
            0x04: "雷达正在加载参数,暂时不能开始工作",
            0x05: "处于工作状态,设置失败",
        }.get(status_code, "未知错误")
        if status == "设置正确":
            logger.info(f"雷达{self.id}工作控制成功，状态为{status}")
        else:
            logger.error(f"雷达{self.id}工作控制失败，状态为{status}")
            return f"雷达{self.id}工作控制失败，状态为{status}"

        db_this_radar = client[self.id]
        scene_parameter_collection = db_this_radar["scene_parameter"]
        this_radar_state = radar_collection.find_one({"ID": self.id})

        if extend == 0x00:
            mission_id_doc = scene_parameter_collection.find_one(
                {"code_name": "MissionID"}
            )
            mission_id_doc = cast(Dict[str, Any], mission_id_doc)
            mission_id = str(mission_id_doc["data"])
            if not db_base_data.find_one({"ID": self.id, "mission_ID.ID": mission_id}):
                # 任务命名为当前时间
                time_now_str = datetime.now().strftime("%Y-%m-%d %H:%M")
                now_coordinates = this_radar_state["radar_coordinates"]
                radar_zero_theta = scene_parameter_collection.find_one(
                    {"code_name": "RadarOri"}
                )
                radar_collection.update_one(
                    {"ID": self.id},
                    {
                        "$addToSet": {
                            "mission_ID": {
                                "ID": mission_id,
                                "name": time_now_str,
                                "coordinates": now_coordinates,
                                "radar_zero_theta": radar_zero_theta,
                            }
                        }
                    },
                )
            path_file = os.path.join(BASE_FILE_PATH, self.id, "work_data", mission_id)
            if not os.path.exists(path_file):
                os.makedirs(os.path.join(path_file, "img_data"))
                os.makedirs(os.path.join(path_file, "deformation_data"))
                os.makedirs(os.path.join(path_file, "Confidence_data"))
                logger.info(f"成功创建雷达{self.id}的工作数据文件夹")
            radar_collection.update_one({"ID": self.id}, {"$set": {"is_work": 1}})
            logger.info(f"雷达{self.id}开始工作")
            return "success"
        elif extend == 0x01:
            radar_collection.update_one({"ID": self.id}, {"$set": {"is_work": 0}})
            logger.info(f"雷达{self.id}停止工作")
            return "success"
        elif extend == 0x02:
            radar_collection.update_one({"ID": self.id}, {"$set": {"is_online": 1}})
            logger.info(f"雷达{self.id}重启")
            return "success"
        else:
            logger.error(f"工作指令响应结果的扩展码错误: {extend}")
            return "工作指令响应结果的扩展码错误"

    def radar_disconnect(self):
        if self.id:
            logger.info(f"雷达 {self.id} 已断开连接")
            radar_collection.update_one(
                {"ID": self.id}, {"$set": {"$set": {"is_online": 0}}}
            )
            self.socket.close()
        else:
            logger.info(f"Radar disconnected")


def tcp_server():
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((HOST, PORT))
    server_socket.listen(10)
    server_socket.setblocking(False)  # select 模式下，主socket应为非阻塞

    logger.info("雷达服务器已启动，监听端口{}:{}".format(HOST, PORT))

    sockets_list = [server_socket]

    while True:
        try:
            readable_sockets, _, exceptional_sockets = select.select(
                sockets_list, [], sockets_list, 1
            )

            for sock in readable_sockets:
                if sock == server_socket:
                    # 新连接
                    client_socket, client_address = server_socket.accept()
                    client_socket.setblocking(False)  # 新的客户端socket也设为非阻塞
                    sockets_list.append(client_socket)

                    # 创建ArcSAR实例并存入映射
                    ArcSAR_map[client_socket] = ArcSAR(client_socket, client_address)
                    logger.info("来自{}的新连接".format(client_address))
                else:
                    # 已有连接发来数据
                    radar_client = ArcSAR_map.get(sock)
                    if radar_client is None:
                        logger.warning(f"从 {sock} 套接字获取的ArcSAR对象不是有效的")
                        continue
                    try:
                        data = radar_client.radar_report()
                        if not data:
                            sockets_list.remove(sock)
                            radar_client.radar_disconnect()

                            del ArcSAR_map[sock]
                            del ArcSAR_id_map[radar_client.id]
                    except Exception as e:
                        logger.error("处理主动主动上报数据时发生错误:{}".format(e))
                        sockets_list.remove(sock)
                        sock.close()

            # 处理异常的socket
            for sock in exceptional_sockets:
                logger.warning(f"处理{sock.getpeername()}的异常情况")
                if sock in sockets_list:
                    sockets_list.remove(sock)
                if sock in ArcSAR_map:
                    del ArcSAR_map[sock]
                    del ArcSAR_id_map[ArcSAR_map[sock].id]
                sock.close()

        except (ConnectionResetError, ConnectionAbortedError) as e:
            logger.error(f"连接被强制关闭: {e}")
            break  # 连接被强制关闭时退出循环
        except Exception as e:
            logger.error(f"主循环中发生严重错误: {e}")
            break  # 出现严重错误时退出循环

    server_socket.close()


if __name__ == "__main__":
    tcp_server()
