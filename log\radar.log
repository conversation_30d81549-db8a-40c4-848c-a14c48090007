2025-06-15 16:30:26,468 - INFO - 服务器线程已启动。
2025-06-15 16:30:26,468 - INFO - [雷达服务器] 雷达服务器已启动，监听端口127.0.0.1:1030
2025-06-15 16:30:27,470 - INFO - 雷达模拟器线程已启动。
2025-06-15 16:30:27,470 - INFO - [模拟器] 已连接到服务器 127.0.0.1:1030
2025-06-15 16:30:27,470 - INFO - [模拟器] 发送初始化注册包...
2025-06-15 16:30:27,471 - INFO - [雷达服务器] 来自('127.0.0.1', 50574)的新连接
2025-06-15 16:30:27,471 - INFO - [雷达(未注册)] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:27,471 - INFO - [雷达(未注册)] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:27,471 - INFO - [雷达(未注册)] 头部数据为
  00000000:  3C 3C AD 05 10 00 40 06 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:27,471 - INFO - [雷达(未注册)] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:27,471 - INFO - [雷达(未注册)] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:27,471 - INFO - [雷达(未注册)] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:27,471 - INFO - [雷达(未注册)] 主要数据为空
2025-06-15 16:30:27,471 - INFO - [雷达001005AD] 来自('127.0.0.1', 50574)的雷达的设备ID为: 001005AD
2025-06-15 16:30:27,490 - INFO - [雷达001005AD] 已经在数据库中注册
2025-06-15 16:30:27,491 - INFO - [雷达001005AD] 重新上线，更新在线状态为1
2025-06-15 16:30:27,491 - INFO - [雷达001005AD] 客户端发送了数据，命令码=0x40, 扩展码=0x06, 主要数据长度=0
2025-06-15 16:30:27,491 - INFO - [雷达001005AD] 服务器分配处理函数为update_radar_time
2025-06-15 16:30:27,491 - INFO - [雷达001005AD] 成功更新雷达的时间戳为1749976227, 对应时间为2025-06-15 16:30:27
2025-06-15 16:30:27,491 - INFO - [雷达001005AD] 服务器成功发送响应数据，长度为4，内容为A3 84 4E 68
2025-06-15 16:30:27,491 - INFO - [模拟器] 收到服务器对注册包的响应，完成注册和shou'shi。授时结果为2025-06-15 16:30:27
2025-06-15 16:30:29,470 - INFO - === STARTING TEST SUITE FOR RADAR [001005AD] ===
2025-06-15 16:30:29,470 - INFO - --- [TEST] RUNNING: Query Radar State ---
2025-06-15 16:30:29,470 - INFO - [雷达001005AD] 开始查询雷达的状态
2025-06-15 16:30:29,470 - INFO - [雷达001005AD] 已将counter=1的状态查询指令放入队列
2025-06-15 16:30:29,508 - INFO - [模拟器] 收到指令: cmd=0x02, ext=0x00, counter=1
2025-06-15 16:30:29,508 - INFO - [模拟器] 已为 counter 1 发送响应 (载荷长度: 0)
2025-06-15 16:30:29,508 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:29,508 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:29,509 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:29,509 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 02 00 01 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:29,509 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:29,509 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:29,509 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:29,509 - INFO - [雷达001005AD] 主要数据为空
2025-06-15 16:30:29,509 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=1
2025-06-15 16:30:29,509 - INFO - [雷达001005AD] 雷达状态查询成功，状态为设置正确
2025-06-15 16:30:29,509 - INFO - 状态查询结果: success
2025-06-15 16:30:29,509 - INFO - --- [TEST] PASSED: Query Radar State ---
2025-06-15 16:30:30,510 - INFO - --- [TEST] RUNNING: Work Control ---
2025-06-15 16:30:30,510 - INFO - Testing: Start Work (0x00)
2025-06-15 16:30:30,510 - INFO - [雷达001005AD] 开始向雷达发送工作控制指令:开始工作
2025-06-15 16:30:30,510 - INFO - [雷达001005AD] 已将counter=2的工作控制指令放入队列:开始工作
2025-06-15 16:30:30,514 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x00, counter=2
2025-06-15 16:30:30,514 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:30,514 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:30,514 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:30,514 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 03 00 01 02 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:30,514 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:30,514 - INFO - [模拟器] 已为 counter 2 发送响应 (载荷长度: 0)
2025-06-15 16:30:30,514 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:30,515 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:30,515 - INFO - [雷达001005AD] 主要数据为空
2025-06-15 16:30:30,515 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=2
2025-06-15 16:30:30,518 - INFO - [雷达001005AD] 雷达开始工作
2025-06-15 16:30:30,518 - INFO - 开始工作结果: success
2025-06-15 16:30:31,019 - INFO - Testing: Stop Work (0x01)
2025-06-15 16:30:31,019 - INFO - [雷达001005AD] 开始向雷达发送工作控制指令:停止工作
2025-06-15 16:30:31,019 - INFO - [雷达001005AD] 已将counter=3的工作控制指令放入队列:停止工作
2025-06-15 16:30:31,515 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:31,515 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x01, counter=3
2025-06-15 16:30:31,515 - INFO - [模拟器] 已为 counter 3 发送响应 (载荷长度: 0)
2025-06-15 16:30:31,515 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:31,515 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:31,515 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 03 01 01 03 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:31,516 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:31,516 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:31,516 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:31,516 - INFO - [雷达001005AD] 主要数据为空
2025-06-15 16:30:31,516 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=3
2025-06-15 16:30:31,518 - INFO - [雷达001005AD] 雷达停止工作
2025-06-15 16:30:31,518 - INFO - 停止工作结果: success
2025-06-15 16:30:32,019 - INFO - Testing: Reboot (0x02)
2025-06-15 16:30:32,019 - INFO - [雷达001005AD] 开始向雷达发送工作控制指令:重启雷达
2025-06-15 16:30:32,019 - INFO - [雷达001005AD] 已将counter=4的工作控制指令放入队列:重启雷达
2025-06-15 16:30:32,532 - INFO - [模拟器] 收到指令: cmd=0x03, ext=0x02, counter=4
2025-06-15 16:30:32,532 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:32,532 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:32,532 - INFO - [模拟器] 已为 counter 4 发送响应 (载荷长度: 0)
2025-06-15 16:30:32,532 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:32,532 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 03 02 01 04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:32,532 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:32,533 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:32,533 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:32,533 - INFO - [雷达001005AD] 主要数据为空
2025-06-15 16:30:32,533 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=4
2025-06-15 16:30:32,534 - INFO - [雷达001005AD] 雷达重启
2025-06-15 16:30:32,534 - INFO - 重启雷达结果: success
2025-06-15 16:30:32,534 - INFO - --- [TEST] PASSED: Work Control ---
2025-06-15 16:30:33,535 - INFO - --- [TEST] RUNNING: Scene Parameter Management ---
2025-06-15 16:30:33,535 - INFO - Testing: Set Valid Scene Parameters
2025-06-15 16:30:33,535 - INFO - [雷达001005AD] 开始设置雷达的场景参数
2025-06-15 16:30:33,535 - INFO - [雷达001005AD] 雷达场景参数校验成功
2025-06-15 16:30:33,535 - INFO - [雷达001005AD] 已将counter=5的场景参数设置指令放入队列
2025-06-15 16:30:33,541 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:33,541 - INFO - [模拟器] 收到指令: cmd=0x00, ext=0x00, counter=5
2025-06-15 16:30:33,541 - INFO - [模拟器] 已为 counter 5 发送响应 (载荷长度: 0)
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 00 00 01 05 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 主要数据为空
2025-06-15 16:30:33,541 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=5
2025-06-15 16:30:33,541 - INFO - [雷达001005AD] 雷达场景参数设置成功，状态为设置正确
2025-06-15 16:30:33,541 - INFO - 设置有效参数结果: success
2025-06-15 16:30:34,042 - INFO - Testing: Query Scene Parameters
2025-06-15 16:30:34,042 - INFO - [雷达001005AD] 开始查询雷达的场景参数
2025-06-15 16:30:34,042 - INFO - [雷达001005AD] 已将counter=6的场景参数查询指令放入队列
2025-06-15 16:30:34,556 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:34,556 - INFO - [模拟器] 收到指令: cmd=0x01, ext=0x00, counter=6
2025-06-15 16:30:34,556 - INFO - [模拟器] 构建【查询场景参数】的响应载荷...
2025-06-15 16:30:34,556 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:34,556 - INFO - [模拟器] 已为 counter 6 发送响应 (载荷长度: 106)
2025-06-15 16:30:34,557 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:34,557 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 01 00 01 06 00 00 00 00 00 00 00 00 00 00 00 00 00 00 6A 00 00 00
2025-06-15 16:30:34,557 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为106
2025-06-15 16:30:34,557 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为106
2025-06-15 16:30:34,557 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为106
2025-06-15 16:30:34,557 - INFO - [雷达001005AD] 主要数据为
  00000000:  1E 00 00 00 00 00 00 80 34 43 00 00 20 41 00 40 9C 45 00 00 00 00 14 00 00 00 00 00
  0000001c:  00 00 00 00 00 00 00 00 00 00 05 00 05 00 01 00 02 00 01 00 01 00 01 00 39 30 00 00
  00000038:  00 00 00 00 02 00 05 00 29 5C 8F C2 F5 18 5D 40 14 AE 47 E1 7A F4 43 40 00 00 00 00
  00000054:  00 00 49 40 00 00 B4 42 00 00 00 3F 00 00 40 40 00 00 00 00 00 00                  
2025-06-15 16:30:34,557 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=6
2025-06-15 16:30:34,564 - INFO - [雷达001005AD] 设备场景参数查询成功
2025-06-15 16:30:34,564 - INFO - 查询参数结果: success
2025-06-15 16:30:35,065 - INFO - Testing: Set Invalid Scene Parameters (should fail validation)
2025-06-15 16:30:35,065 - INFO - [雷达001005AD] 开始设置雷达的场景参数
2025-06-15 16:30:35,065 - ERROR - [雷达001005AD] 雷达场景参数校验失败: 20 validation errors for SceneParameter
RoRate
  Input should be 0, 20, 30, 50 or 60 [type=literal_error, input_value=99, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/literal_error
RotAngBgn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RotAngEnd
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarMode
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
PS_TD
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
StartStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
EndStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
ScatImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
DefoImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionIDSwitch
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionID
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntBWAz
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntSteerVt
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLon
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLat
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarHei
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarOri
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarArmLen
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
FilterType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
LocaltionType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-15 16:30:35,065 - INFO - 设置无效参数结果: [雷达001005AD] 雷达场景参数校验失败: 20 validation errors for SceneParameter
RoRate
  Input should be 0, 20, 30, 50 or 60 [type=literal_error, input_value=99, input_type=int]
    For further information visit https://errors.pydantic.dev/2.11/v/literal_error
RotAngBgn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RotAngEnd
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarMode
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
PS_TD
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
StartStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
EndStopTime
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
ScatImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
DefoImageEn
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionIDSwitch
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
MissionID
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntBWAz
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
AntSteerVt
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLon
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarLat
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarHei
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarOri
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
RadarArmLen
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
FilterType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
LocaltionType
  Field required [type=missing, input_value={'RoRate': 99, 'RangeMin'...0.0, 'RangeMax': 5000.0}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.11/v/missing
2025-06-15 16:30:35,065 - INFO - --- [TEST] PASSED: Scene Parameter Management ---
2025-06-15 16:30:36,066 - INFO - --- [TEST] RUNNING: Atmospheric Correction Control ---
2025-06-15 16:30:36,066 - INFO - Testing: Turn On (0x01)
2025-06-15 16:30:36,066 - INFO - [雷达001005AD] 开始向雷达发送大气校正控制指令:大气校正开
2025-06-15 16:30:36,066 - INFO - [雷达001005AD] 已将counter=8的大气校正控制指令放入队列:大气校正开
2025-06-15 16:30:36,582 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:36,582 - INFO - [模拟器] 收到指令: cmd=0x05, ext=0x01, counter=8
2025-06-15 16:30:36,582 - INFO - [模拟器] 已为 counter 8 发送响应 (载荷长度: 0)
2025-06-15 16:30:36,582 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:36,582 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:36,582 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 05 01 01 08 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:36,582 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:36,582 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:36,582 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:36,583 - INFO - [雷达001005AD] 主要数据为空
2025-06-15 16:30:36,583 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=8
2025-06-15 16:30:36,583 - INFO - [雷达001005AD] 大气校正开控制成功
2025-06-15 16:30:36,583 - INFO - 开启大气校正结果: success
2025-06-15 16:30:37,084 - INFO - Testing: Turn Off (0x00)
2025-06-15 16:30:37,084 - INFO - [雷达001005AD] 开始向雷达发送大气校正控制指令:大气校正关
2025-06-15 16:30:37,084 - INFO - [雷达001005AD] 已将counter=9的大气校正控制指令放入队列:大气校正关
2025-06-15 16:30:37,593 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:37,593 - INFO - [模拟器] 收到指令: cmd=0x05, ext=0x00, counter=9
2025-06-15 16:30:37,593 - INFO - [模拟器] 已为 counter 9 发送响应 (载荷长度: 0)
2025-06-15 16:30:37,593 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:37,593 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:37,594 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 05 00 01 09 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
2025-06-15 16:30:37,594 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为0
2025-06-15 16:30:37,594 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为0
2025-06-15 16:30:37,594 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为0
2025-06-15 16:30:37,594 - INFO - [雷达001005AD] 主要数据为空
2025-06-15 16:30:37,594 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=9
2025-06-15 16:30:37,594 - INFO - [雷达001005AD] 大气校正关控制成功
2025-06-15 16:30:37,594 - INFO - 关闭大气校正结果: success
2025-06-15 16:30:37,594 - INFO - --- [TEST] PASSED: Atmospheric Correction Control ---
2025-06-15 16:30:38,595 - INFO - --- [TEST] RUNNING: Log File Management ---
2025-06-15 16:30:38,595 - INFO - Testing: Query Log File List
2025-06-15 16:30:38,595 - INFO - [雷达001005AD] 开始查询雷达的日志
2025-06-15 16:30:38,595 - INFO - [雷达001005AD] 已将counter=10的日志查询指令放入队列
2025-06-15 16:30:38,609 - INFO - [模拟器] 收到指令: cmd=0x08, ext=0x00, counter=10
2025-06-15 16:30:38,609 - INFO - [模拟器] 构建【查询日志列表】的响应载荷...
2025-06-15 16:30:38,609 - INFO - [模拟器] 已为 counter 10 发送响应 (载荷长度: 49)
2025-06-15 16:30:38,609 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 08 00 01 0A 00 00 00 00 00 00 00 00 00 00 00 00 00 00 31 00 00 00
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为49
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为49
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为49
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 主要数据为
  00000000:  72 61 64 61 72 5F 32 30 32 34 2D 30 36 2D 31 38 2E 6C 6F 67 2C 73 79 73 74 65 6D 5F
  0000001c:  65 76 65 6E 74 73 2E 6C 6F 67 2C 63 6F 6E 66 69 67 2E 74 78 74                     
2025-06-15 16:30:38,609 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=10
2025-06-15 16:30:38,609 - INFO - [雷达001005AD] 雷达日志查询成功
2025-06-15 16:30:38,610 - INFO - [雷达001005AD] 日志radar_2024-06-18.log已存在
2025-06-15 16:30:38,612 - INFO - [雷达001005AD] 日志system_events.log已存在
2025-06-15 16:30:38,612 - INFO - [雷达001005AD] 日志config.txt已存在
2025-06-15 16:30:38,613 - INFO - [雷达001005AD] 雷达日志查询成功
2025-06-15 16:30:38,613 - INFO - 查询日志列表结果: success
2025-06-15 16:30:39,115 - INFO - Testing: Download Log File 'radar_2024-06-18.log'
2025-06-15 16:30:39,115 - INFO - [雷达001005AD] 开始下载雷达日志radar_2024-06-18.log
2025-06-15 16:30:39,115 - INFO - [雷达001005AD] 已将counter=11的日志下载指令放入队列
2025-06-15 16:30:39,618 - INFO - [模拟器] 收到指令: cmd=0x08, ext=0x01, counter=11
2025-06-15 16:30:39,619 - INFO - [雷达服务器] 成功向雷达001005AD发送指令
2025-06-15 16:30:39,619 - INFO - [模拟器] 构建【下载日志文件: radar_2024-06-18.log】的响应载荷...
2025-06-15 16:30:39,619 - INFO - [模拟器] 已为 counter 11 发送响应 (载荷长度: 226)
2025-06-15 16:30:39,619 - INFO - [雷达001005AD] 正从('127.0.0.1', 50574)处接受头部数据，目标长度为28
2025-06-15 16:30:39,619 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到头部数据，长度为28
2025-06-15 16:30:39,619 - INFO - [雷达001005AD] 头部数据为
  00000000:  3C 3C AD 05 10 00 08 01 01 0B 00 00 00 00 00 00 00 00 00 00 00 00 00 00 E2 00 00 00
2025-06-15 16:30:39,619 - INFO - [雷达001005AD] 从('127.0.0.1', 50574)解析得到的数据长度为226
2025-06-15 16:30:39,619 - INFO - [雷达001005AD] 开始从('127.0.0.1', 50574)处接受数据，目标长度为226
2025-06-15 16:30:39,619 - INFO - [雷达001005AD] 成功在('127.0.0.1', 50574)接收到主要数据，长度为226
2025-06-15 16:30:39,619 - INFO - [雷达001005AD] 主要数据为
  00000000:  72 61 64 61 72 5F 32 30 32 34 2D 30 36 2D 31 38 2E 6C 6F 67 00 00 00 00 00 00 00 00
  0000001c:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  00000038:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  00000054:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
  00000070:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 4C 6F 67 20 65 6E 74 72 79 20 66 6F
  0000008c:  72 20 72 61 64 61 72 5F 32 30 32 34 2D 30 36 2D 31 38 2E 6C 6F 67 0A 54 69 6D 65 73
  000000a8:  74 61 6D 70 3A 20 32 30 32 35 2D 30 36 2D 31 35 20 31 36 3A 33 30 3A 33 39 2E 36 31
  000000c4:  39 33 36 34 0A 54 68 69 73 20 69 73 20 61 20 66 61 6B 65 20 6C 6F 67 20 65 6E 74 72
  000000e0:  79 2E                                                                              
2025-06-15 16:30:39,620 - INFO - [雷达服务器] 收到雷达001005AD的响应，counter=11
2025-06-15 16:30:39,620 - INFO - [雷达001005AD] 雷达日志下载指令设置成功
2025-06-15 16:30:39,622 - INFO - [雷达001005AD] 雷达日志下载成功
2025-06-15 16:30:39,623 - INFO - 下载日志文件结果: success
2025-06-15 16:30:39,623 - INFO - 已验证文件 '001005AD\radar_file\radar_2024-06-18.log' 创建成功且内容正确。
2025-06-15 16:30:39,623 - INFO - --- [TEST] PASSED: Log File Management ---
2025-06-15 16:30:39,623 - INFO - === TEST SUITE FOR RADAR [001005AD] COMPLETED ===
2025-06-15 16:30:39,623 - INFO - 主线程结束。守护线程将随之退出。
2025-06-20 13:58:59,144 - INFO - 注册蓝图
2025-06-20 13:58:59,149 - INFO - 配置数据库并连接
2025-06-20 13:58:59,149 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 13:58:59,173 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 13:58:59,173 - INFO - 数据库连接成功
2025-06-20 13:58:59,173 - INFO - 获取数据库连接
2025-06-20 13:58:59,173 - INFO - 共享配置初始化完成
2025-06-20 13:58:59,194 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-20 13:58:59,194 - INFO - [33mPress CTRL+C to quit[0m
2025-06-20 13:58:59,195 - INFO -  * Restarting with stat
2025-06-20 13:59:00,213 - INFO - 注册蓝图
2025-06-20 13:59:00,218 - INFO - 配置数据库并连接
2025-06-20 13:59:00,218 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 13:59:00,247 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 13:59:00,247 - INFO - 数据库连接成功
2025-06-20 13:59:00,247 - INFO - 获取数据库连接
2025-06-20 13:59:00,247 - INFO - 共享配置初始化完成
2025-06-20 13:59:00,256 - WARNING -  * Debugger is active!
2025-06-20 13:59:00,261 - INFO -  * Debugger PIN: 378-064-233
2025-06-20 14:00:11,859 - INFO - 注册蓝图
2025-06-20 14:00:11,864 - INFO - 配置数据库并连接
2025-06-20 14:00:11,864 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 14:00:11,898 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 14:00:11,898 - INFO - 数据库连接成功
2025-06-20 14:00:11,898 - INFO - 获取数据库连接
2025-06-20 14:00:11,898 - INFO - 共享配置初始化完成
2025-06-20 14:00:11,911 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-20 14:00:11,911 - INFO - [33mPress CTRL+C to quit[0m
2025-06-20 14:00:11,912 - INFO -  * Restarting with stat
2025-06-20 14:00:12,917 - INFO - 注册蓝图
2025-06-20 14:00:12,921 - INFO - 配置数据库并连接
2025-06-20 14:00:12,921 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 14:00:12,935 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 14:00:12,935 - INFO - 数据库连接成功
2025-06-20 14:00:12,935 - INFO - 获取数据库连接
2025-06-20 14:00:12,935 - INFO - 共享配置初始化完成
2025-06-20 14:00:12,943 - WARNING -  * Debugger is active!
2025-06-20 14:00:12,949 - INFO -  * Debugger PIN: 378-064-233
2025-06-20 14:31:07,975 - INFO - 注册蓝图
2025-06-20 14:31:07,980 - INFO - 配置数据库并连接
2025-06-20 14:31:07,980 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 14:31:07,984 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 14:31:07,984 - INFO - 数据库连接成功
2025-06-20 14:31:07,984 - INFO - 获取数据库连接
2025-06-20 14:31:07,984 - INFO - 共享配置初始化完成
2025-06-20 14:31:07,997 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-20 14:31:07,997 - INFO - [33mPress CTRL+C to quit[0m
2025-06-20 14:31:07,998 - INFO -  * Restarting with stat
2025-06-20 14:31:09,036 - INFO - 注册蓝图
2025-06-20 14:31:09,041 - INFO - 配置数据库并连接
2025-06-20 14:31:09,041 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 14:31:09,082 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 14:31:09,082 - INFO - 数据库连接成功
2025-06-20 14:31:09,082 - INFO - 获取数据库连接
2025-06-20 14:31:09,082 - INFO - 共享配置初始化完成
2025-06-20 14:31:09,090 - WARNING -  * Debugger is active!
2025-06-20 14:31:09,095 - INFO -  * Debugger PIN: 378-064-233
2025-06-20 14:31:57,290 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\web_code\\user.py', reloading
2025-06-20 14:31:57,914 - INFO -  * Restarting with stat
2025-06-20 14:31:59,051 - INFO - 注册蓝图
2025-06-20 14:31:59,056 - INFO - 配置数据库并连接
2025-06-20 14:31:59,056 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 14:31:59,083 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 14:31:59,083 - INFO - 数据库连接成功
2025-06-20 14:31:59,083 - INFO - 获取数据库连接
2025-06-20 14:31:59,083 - INFO - 共享配置初始化完成
2025-06-20 14:31:59,092 - WARNING -  * Debugger is active!
2025-06-20 14:31:59,098 - INFO -  * Debugger PIN: 378-064-233
2025-06-20 14:32:02,320 - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\web_code\\user.py', reloading
2025-06-20 14:32:02,879 - INFO -  * Restarting with stat
2025-06-20 14:32:03,980 - INFO - 注册蓝图
2025-06-20 14:32:03,985 - INFO - 配置数据库并连接
2025-06-20 14:32:03,985 - INFO - 正在创建并初始化MongoDB单例客户端，URI: 'mongodb://localhost:27017/base_data'
2025-06-20 14:32:04,009 - INFO - MongoDB单例客户端已成功连接并验证。
2025-06-20 14:32:04,009 - INFO - 数据库连接成功
2025-06-20 14:32:04,009 - INFO - 获取数据库连接
2025-06-20 14:32:04,009 - INFO - 共享配置初始化完成
2025-06-20 14:32:04,019 - WARNING -  * Debugger is active!
2025-06-20 14:32:04,025 - INFO -  * Debugger PIN: 378-064-233
2025-06-20 14:34:04,638 - INFO - 127.0.0.1 - - [20/Jun/2025 14:34:04] "[31m[1mGET /user/register HTTP/1.1[0m" 405 -
2025-06-20 14:34:04,948 - INFO - 127.0.0.1 - - [20/Jun/2025 14:34:04] "[33mGET /user/installHook.js.map HTTP/1.1[0m" 404 -
2025-06-20 14:34:05,092 - INFO - 127.0.0.1 - - [20/Jun/2025 14:34:05] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-20 14:34:22,731 - INFO - 127.0.0.1 - - [20/Jun/2025 14:34:22] "[31m[1mGET /user/register HTTP/1.1[0m" 405 -
2025-06-20 14:34:29,611 - INFO - 127.0.0.1 - - [20/Jun/2025 14:34:29] "[31m[1mGET /user/change_password HTTP/1.1[0m" 405 -
2025-06-20 14:34:34,231 - INFO - 127.0.0.1 - - [20/Jun/2025 14:34:34] "[31m[1mGET /user/login HTTP/1.1[0m" 405 -
2025-06-20 14:34:54,710 - INFO - 127.0.0.1 - - [20/Jun/2025 14:34:54] "[31m[1mGET /listen_radar_state HTTP/1.1[0m" 405 -
2025-06-20 14:35:03,162 - INFO - 127.0.0.1 - - [20/Jun/2025 14:35:03] "[31m[1mGET /user/login HTTP/1.1[0m" 405 -
2025-06-20 14:36:21,153 - INFO - 127.0.0.1 - - [20/Jun/2025 14:36:21] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:36:21,153 - INFO - 127.0.0.1 - - [20/Jun/2025 14:36:21] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:36:21,157 - INFO - 127.0.0.1 - - [20/Jun/2025 14:36:21] "[31m[1mPOST /get_scene_list HTTP/1.1[0m" 422 -
2025-06-20 14:36:21,158 - INFO - 127.0.0.1 - - [20/Jun/2025 14:36:21] "[31m[1mPOST /get_scene_list HTTP/1.1[0m" 422 -
2025-06-20 14:36:25,887 - INFO - 127.0.0.1 - - [20/Jun/2025 14:36:25] "[31m[1mPOST /get_scene_list HTTP/1.1[0m" 422 -
2025-06-20 14:36:25,888 - INFO - 127.0.0.1 - - [20/Jun/2025 14:36:25] "[31m[1mPOST /get_scene_list HTTP/1.1[0m" 422 -
2025-06-20 14:37:02,016 - INFO - 127.0.0.1 - - [20/Jun/2025 14:37:02] "OPTIONS /user/login HTTP/1.1" 200 -
2025-06-20 14:37:02,020 - INFO - 127.0.0.1 - - [20/Jun/2025 14:37:02] "POST /user/login HTTP/1.1" 200 -
2025-06-20 14:37:03,065 - INFO - 127.0.0.1 - - [20/Jun/2025 14:37:03] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:37:03,066 - INFO - 127.0.0.1 - - [20/Jun/2025 14:37:03] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:37:03,069 - ERROR - 获取当前用户可访问的场景列表失败: 'dict' object is not callable
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 769, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 842, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\app.py", line 163, in web_get_scene_list
    validate(identity_dict("username"), "用户名").required().not_empty().unwrap()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'dict' object is not callable
2025-06-20 14:37:03,071 - ERROR - 获取当前用户可访问的场景列表失败: 'dict' object is not callable
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 769, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 842, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\app.py", line 163, in web_get_scene_list
    validate(identity_dict("username"), "用户名").required().not_empty().unwrap()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'dict' object is not callable
2025-06-20 14:37:03,073 - INFO - 127.0.0.1 - - [20/Jun/2025 14:37:03] "[35m[1mPOST /get_scene_list HTTP/1.1[0m" 500 -
2025-06-20 14:37:03,073 - INFO - 127.0.0.1 - - [20/Jun/2025 14:37:03] "[35m[1mPOST /get_scene_list HTTP/1.1[0m" 500 -
2025-06-20 14:54:24,863 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:24] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:54:24,864 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:24] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:54:24,880 - ERROR - 获取当前用户可访问的场景列表失败: 'dict' object is not callable
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 769, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 842, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\app.py", line 163, in web_get_scene_list
    validate(identity_dict("username"), "用户名").required().not_empty().unwrap()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'dict' object is not callable
2025-06-20 14:54:24,883 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:24] "[35m[1mPOST /get_scene_list HTTP/1.1[0m" 500 -
2025-06-20 14:54:24,885 - ERROR - 获取当前用户可访问的场景列表失败: 'dict' object is not callable
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 769, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 842, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\app.py", line 163, in web_get_scene_list
    validate(identity_dict("username"), "用户名").required().not_empty().unwrap()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'dict' object is not callable
2025-06-20 14:54:24,889 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:24] "[35m[1mPOST /get_scene_list HTTP/1.1[0m" 500 -
2025-06-20 14:54:53,221 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:53] "OPTIONS /user/login HTTP/1.1" 200 -
2025-06-20 14:54:53,226 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:53] "POST /user/login HTTP/1.1" 200 -
2025-06-20 14:54:54,164 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:54] "POST /user/login HTTP/1.1" 200 -
2025-06-20 14:54:54,321 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:54] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:54:54,324 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:54] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:54:54,329 - ERROR - 获取当前用户可访问的场景列表失败: 'dict' object is not callable
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 769, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 842, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\app.py", line 163, in web_get_scene_list
    validate(identity_dict("username"), "用户名").required().not_empty().unwrap()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'dict' object is not callable
2025-06-20 14:54:54,330 - ERROR - 获取当前用户可访问的场景列表失败: 'dict' object is not callable
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 769, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 842, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\app.py", line 163, in web_get_scene_list
    validate(identity_dict("username"), "用户名").required().not_empty().unwrap()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'dict' object is not callable
2025-06-20 14:54:54,331 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:54] "[35m[1mPOST /get_scene_list HTTP/1.1[0m" 500 -
2025-06-20 14:54:54,331 - INFO - 127.0.0.1 - - [20/Jun/2025 14:54:54] "[35m[1mPOST /get_scene_list HTTP/1.1[0m" 500 -
2025-06-20 14:55:09,459 - INFO - 127.0.0.1 - - [20/Jun/2025 14:55:09] "OPTIONS /user/login HTTP/1.1" 200 -
2025-06-20 14:55:09,465 - INFO - 127.0.0.1 - - [20/Jun/2025 14:55:09] "POST /user/login HTTP/1.1" 200 -
2025-06-20 14:55:09,488 - INFO - 127.0.0.1 - - [20/Jun/2025 14:55:09] "OPTIONS /get_scene_list HTTP/1.1" 200 -
2025-06-20 14:55:09,492 - ERROR - 获取当前用户可访问的场景列表失败: 'dict' object is not callable
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 769, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\validator\validator_framework.py", line 842, in decorated_function
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Arcweb\backend\app.py", line 163, in web_get_scene_list
    validate(identity_dict("username"), "用户名").required().not_empty().unwrap()
             ^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: 'dict' object is not callable
2025-06-20 14:55:09,493 - INFO - 127.0.0.1 - - [20/Jun/2025 14:55:09] "[35m[1mPOST /get_scene_list HTTP/1.1[0m" 500 -
2025-06-22 19:42:04,020 - INFO - 服务器线程已启动。
2025-06-22 19:42:53,895 - INFO - 服务器线程已启动。
2025-06-22 19:42:53,895 - INFO - [雷达服务器] 雷达服务器已启动，监听端口192.168.1.2:1030
2025-06-22 19:43:38,095 - INFO - 服务器线程已启动。
2025-06-22 19:43:38,095 - INFO - [雷达服务器] 雷达服务器已启动，监听端口192.168.1.2:1030
2025-06-22 19:43:39,096 - INFO - {}
2025-06-22 19:45:22,186 - INFO - 服务器线程已启动。
2025-06-22 19:45:22,188 - INFO - [雷达服务器] 雷达服务器已启动，监听端口192.168.1.2:1030
2025-06-22 19:45:23,188 - ERROR - !!! CRITICAL TEST FAILURE: No radar connected. Aborting all tests. !!!
