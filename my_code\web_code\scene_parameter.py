"""
场景参数模块
提供雷达场景参数的配置、管理和应用功能
"""

from bson import ObjectId
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
import my_code.radar_code as radar_code
from app import (
    mongo,
    radar_collection,
    scene_collection,
    user_collection,
    logger,
    ApiResponse,
)
from validator import *
import http

# 创建蓝图
scene_parameter = Blueprint("scene_parameter", __name__)


# 查询成像参数
@scene_parameter.route("/check_scene_parameters", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="查询场景参数")
@handle_database_exceptions
@validate_request("radar_ID")
def web_check_scene_parameter(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]

    db_this_radar = mongo[radar_id]
    result = radar_code.ArcSAR_id_map[radar_id].query_scene_parameter()
    scene_parameter_collection = db_this_radar["scene_parameter"]
    result_doc = scene_parameter_collection.find(
        {"serial_number": {"$gte": 1, "$lte": 24}}
    )
    scene_parameter_data = {}
    for each_doc in result_doc:
        scene_parameter_data[each_doc["code_name"]] = str(each_doc["data"])
    if result == "success":
        return (
            jsonify(
                {
                    "states": "success",  # 状态信息
                    "message": "查询成功",  # 附加消息
                    "data": scene_parameter_data,  # 数据
                }
            ),
            200,
        )
    elif result == "error":
        return (
            jsonify(
                {
                    "states": "warning",
                    "message": "查询失败，数据为雷达最后在线时的算法参数",
                    "data": scene_parameter_data,
                }
            ),
            500,
        )
    else:
        return (
            jsonify(
                {"states": "error", "message": result, "data": scene_parameter_data}
            ),
            500,
        )


# 更新成像参数
@scene_parameter.route("/update_scene_parameter", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="更新场景参数")
@handle_database_exceptions
@validate_request("radar_ID")
def web_update_scene_parameter(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    scene_parameter = kwargs.pop("radar_ID", None)
    result = radar_code.ArcSAR_id_map[radar_id].set_scene_parameter(scene_parameter)
    if result == "success":
        return jsonify({"states": "success", "message": "雷达成像参数设置成功"}), 200
    elif result == "error":
        return jsonify({"states": "error", "message": "雷达成像参数设置失败"}), 500
    else:
        return jsonify({"states": "warning", "message": result}), 500
