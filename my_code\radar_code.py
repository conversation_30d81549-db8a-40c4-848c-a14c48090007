import socket
import select
import struct
from collections import namedtuple
from logging import getLogger, basicConfig, INFO
from enum import Enum
from pymongo import MongoClient
import pandas as pd
import os
from typing import (
    Optional,
    Tuple,
    cast,
    Dict,
    List,
    Any,
    Union,
    Optional,
    Callable,
    Iterable,
    Generator,
    Literal,
)
import hashlib
import snappy  # type: ignore
import numpy as np
from tifffile import imwrite, imread  # type: ignore
import time
import multiprocessing as mp
from scipy.interpolate import griddata  # type: ignore
import matplotlib.pyplot as plt
from queue import Queue
from threading import Event
from datetime import datetime
from pydantic import BaseModel, Field, model_validator, ValidationError

# --- 配置日志，方便调试 ---
logger = getLogger(__name__)
# 结束后，日志写入到文件中
if not os.path.exists("log"):
    os.mkdir("log")
basicConfig(
    filename="log/radar.log",
    level=INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    encoding="utf-8",
)

BASE_FILE_PATH = ""
# HOST: str = "*************"
HOST: str = "127.0.0.1"
PORT: int = 1030

client: MongoClient = MongoClient("mongodb://localhost:27017/")
db_base_data = client["base_data"]
radar_collection = db_base_data["radar"]


class CommandHeader(Enum):
    PLATFORM = 0x5A5A
    RADAR = 0x3C3C


from typing import Literal
from pydantic import BaseModel, Field


class SceneParameter(BaseModel):
    """
    一个基于提供的技术表格精确定义的场景参数Pydantic模型。
    该模型包含了详细的字段描述、别名、类型和严格的验证规则。
    """

    # model_config 用于Pydantic V2版本，等同于旧版的 class Config
    model_config = {
        "populate_by_name": True,  # 允许通过字段名和别名两种方式进行赋值
        "extra": "forbid",  # 禁止传入未在模型中定义的额外字段
    }

    # =================================
    # 扫描参数 (Scanning Parameters)
    # =================================
    rotation_rate: Literal[0, 20, 30, 50, 60] = Field(
        alias="RoRate",
        description="正扫转台转速。0: 停止; 20: 2rpm; 30: 1rpm; 50: 1/4rpm; 60: 1/16rpm。",
    )
    rotation_angle_start_degrees: float = Field(
        alias="RotAngBgn",
        description="转台起始扫描角，格式为 dd.dd°。从转台零位开始，顺时针方向，按最小步进量化。",
    )
    rotation_angle_end_degrees: float = Field(
        alias="RotAngEnd",
        description="转台结束扫描角，格式为 dd.dd°。从转台零位开始，顺时针方向，按最小步进量化。",
    )
    range_min_meters: float = Field(
        alias="RangeMin", ge=0, description="回波最近距离，单位为米。"
    )
    range_max_meters: float = Field(
        alias="RangeMax", ge=0, description="回波最远距离，单位为米。"
    )
    radar_mode: Literal[0, 1] = Field(
        alias="RadarMode",
        description="高分、性能模式切换。0：高分模式，1：性能模式。占用整数低字节。",
    )
    point_selection_threshold: int = Field(
        alias="PS_TD",
        ge=0,
        le=40,
        description="PS筛选门限，范围0-40，数值越大，PS点越多。",
    )

    # =================================
    # 输出参数 (Output Parameters)
    # =================================
    start_dwell_time_seconds: int = Field(
        alias="StartStopTime", ge=0, description="起点停留时间，单位为秒。"
    )
    end_dwell_time_seconds: int = Field(
        alias="EndStopTime", ge=0, description="终点停留时间，单位为秒。"
    )
    is_scatter_image_enabled: Literal[0, 1] = Field(
        alias="ScatImageEn", description="散射图像是否有效。0: 无效; 1: 有效。"
    )
    scatter_image_decimation: int = Field(
        default=0,
        alias="ScatImageDec",
        ge=0,
        description="散射图像帧抽取。0: 无效; 1及以上: 抽取比。缺省值为0。",
    )
    is_defo_image_enabled: Literal[0, 1] = Field(
        alias="DefoImageEn", description="形变图像是否有效。0: 无效; 1: 有效。"
    )
    defo_image_decimation: int = Field(
        default=1,
        alias="DefoImageDec",
        ge=0,
        description="形变图像帧抽取。0: 无效; 1及以上: 抽取比。缺省值为1。",
    )
    is_mission_id_enabled: Literal[0, 1] = Field(
        alias="MissionIDSwitch",
        description="上位机任务ID是否启用。0：雷达自己生成任务ID；1：使用上位机设置任务ID。",
    )
    mission_id: int = Field(
        alias="MissionID", description="任务ID，用于区分不同的扫描任务。"
    )

    # =================================
    # 其他参数 (Other Parameters)
    # =================================
    antenna_azimuth_beam_width_degrees: int = Field(
        alias="AntBWAz", description="天线方位波束角，单位为度。"
    )
    antenna_vertical_steering_angle: int = Field(
        alias="AntSteerVt",
        description="天线舵机俯仰。单位为0.1°，实际角度乘以10后取整。例如，1度表示为10。",
    )
    radar_longitude: float = Field(
        alias="RadarLon",
        ge=-180.0,
        le=180.0,
        description="雷达位置经度，格式为 dd.dddddd°。",
    )
    radar_latitude: float = Field(
        alias="RadarLat",
        ge=-90.0,
        le=90.0,
        description="雷达位置纬度，格式为 dd.dddddd°。",
    )
    radar_height_meters: float = Field(
        alias="RadarHei", description="雷达位置高度，单位为米。"
    )
    radar_orientation_degrees: float = Field(
        alias="RadarOri",
        ge=0,
        lt=360,
        description="雷达转台零点朝向，相对于正北方向顺时针，格式为 dd.dd°。",
    )
    radar_arm_length_meters: float = Field(
        alias="RadarArmLen",
        ge=0,
        description="雷达转臂长度，单位为米。指转轴到天线相位中心的距离。",
    )
    filter_type: float = Field(
        alias="FilterType",
        ge=0,
        le=10,
        description="滤波控制。0: 不滤波; 1-10: 值越大滤波越强。",
    )
    localization_mode: Literal[0, 1] = Field(
        alias="LocaltionType",  # 保持表格中的原始别名(LocaltionType)
        description="定位模式。0: 手动定位; 1: 自动定位。",
    )

    @model_validator(mode="after")
    def check_range_min_max(self) -> "SceneParameter":
        """验证回波最远距离必须大于或等于最近距离"""
        if self.range_min_meters > self.range_max_meters:
            raise ValueError(
                "回波最远距离 (RangeMax) 必须大于或等于回波最近距离 (RangeMin)"
            )
        return self

    def pack_to_bytes(self) -> bytes:
        format_string = "<H 4f 2I 12x 7H I 4x 2H 3d 3f H 4x"
        field_order = [
            "rotation_rate",
            "rotation_angle_start_degrees",
            "rotation_angle_end_degrees",
            "range_min_meters",
            "range_max_meters",
            "radar_mode",
            "point_selection_threshold",
            "start_dwell_time_seconds",
            "end_dwell_time_seconds",
            "is_scatter_image_enabled",
            "scatter_image_decimation",
            "is_defo_image_enabled",
            "defo_image_decimation",
            "is_mission_id_enabled",
            "mission_id",
            "antenna_azimuth_beam_width_degrees",
            "antenna_vertical_steering_angle",
            "radar_longitude",
            "radar_latitude",
            "radar_height_meters",
            "radar_orientation_degrees",
            "radar_arm_length_meters",
            "filter_type",
            "localization_mode",
        ]

        # 关键代码在这里
        values = [getattr(self, key) for key in field_order]

        return struct.pack(format_string, *values)


Radar = namedtuple("Radar", ["Id_str", "Socket"])
radar_list: list[Radar] = []
ArcSAR_map: dict[socket.socket, "ArcSAR"] = {}
ArcSAR_id_map: dict[str, "ArcSAR"] = {}


class ArcSAR:
    """
    ArcSAR类：管理与单个雷达设备的通信和数据处理

    此类封装了与雷达设备通信的所有功能，包括：
    - 雷达状态查询和控制
    - 数据接收和解析
    - 图像数据处理
    - 数据库操作
    """

    HEADER_FORMAT = "<H I B B B I B 10x I"
    HEADER_SIZE = struct.calcsize(HEADER_FORMAT)

    def __init__(self, client_socket: socket.socket, address: tuple[str, int]):
        self.id: str = ""  # 雷达ID，首次通信时设置
        self.socket: socket.socket = client_socket  # 与雷达设备的TCP连接
        self.counter: int = 0  # 用于数据包的序号
        self.address: tuple[str, int] = address  # 雷达设备的IP地址和端口

        # 线程安全的指令队列和响应管理
        self.command_queue: Queue = Queue()  # 用于从其他线程接收指令
        self.pending_responses: Dict[int, Tuple[Event, Dict[str, Any]]] = (
            {}
        )  # key是counter，value是(Event, response_container)

        self.socket.settimeout(5)

    def handle_radar_report(
        self, header_data: bytes, payload_data: Optional[bytes]
    ) -> bool:
        """
        处理已解包的雷达上报数据

        这个方法接收已经解包的头部和载荷数据，避免重复解包

        此方法接收雷达发送的数据，解析指令头部，并根据扩展码调用相应的处理函数。
        处理函数包括：
        - 00: 更新雷达基本信息
        - 01: 上传图像数据
        - 02/03: 上传形变或置信度数据
        - 04: 上传移动目标数据
        - 05: 添加日志文件
        - 06: 更新雷达时间

        :param header_data: 已经解包的头部数据
        :param payload_data: 已经解包的载荷数据
        :return bytes: 处理结果，成功返回b'success'，失败返回b''
        """
        # 解析头部
        try:
            (head, radar_id, command, extend, sign, con_int, state_code, data_len) = (
                struct.unpack(self.HEADER_FORMAT, header_data)
            )
        except struct.error as e:
            logger.error(
                f"[雷达{self.id if self.id else '未知'}] 无法从{self.address}处正确解包头部: {e}"
            )
            return False

        # 如果是第一次通信，设置雷达ID
        if not self.id:
            self.id = f"{radar_id:08X}"
            logger.info(
                f"[雷达{self.id}] 来自{self.address}的雷达的设备ID为: {self.id}"
            )
            ArcSAR_id_map[self.id] = self
            self.register_or_update_online_status()

        # 根据扩展码分派任务
        logger.info(
            f"[雷达{self.id}] 客户端发送了数据，命令码=0x{command:02x}, 扩展码=0x{extend:02x}, 主要数据长度={data_len}"
        )

        # 处理命令码为0x40的主动上报
        if (
            head == CommandHeader.RADAR.value
            and command == 0x40
            and extend in range(0x00, 0x06 + 0x01)
        ):
            dispatch_map = {
                0x00: self.update_radar_imformation,
                0x01: self.upload_image_data,
                0x02: self.upload_defo_or_confi_data,
                0x03: self.upload_defo_or_confi_data,
                0x04: self.upload_moving_target_data,
                0x05: self.upload_log_file,
                0x06: self.update_radar_time,
            }
            logger.info(
                f"[雷达{self.id}] 服务器分配处理函数为{dispatch_map[extend].__name__}"
            )
            respond_data_bytes = dispatch_map[extend](payload_data)
            respond_data_len = len(respond_data_bytes) if respond_data_bytes else 0

            if respond_data_len > 0:
                data = struct.pack(
                    f"{ArcSAR.HEADER_FORMAT} {respond_data_len}s",
                    CommandHeader.PLATFORM.value,
                    radar_id,
                    command,
                    extend,
                    0x01,
                    con_int,
                    0x00,
                    respond_data_len,
                    respond_data_bytes,
                )
            elif respond_data_len == 0:
                data = struct.pack(
                    ArcSAR.HEADER_FORMAT,
                    CommandHeader.PLATFORM.value,
                    radar_id,
                    command,
                    extend,
                    0x01,
                    con_int,
                    0x00,
                    respond_data_len,
                )
            else:
                logger.error(
                    f"[雷达{self.id}] 服务器解包得到命令码0x{command:02x}, 扩展码0x{extend:02x}，但响应数据长度为{respond_data_len}，不符合协议"
                )
                return False

            self.socket.sendall(data)
            logger.info(
                f"[雷达{self.id}] 服务器成功发送响应数据，长度为{respond_data_len}，内容为{respond_data_bytes.hex(' ').upper() if respond_data_bytes else '空'}"
            )
        else:
            logger.warning(
                f"[雷达{self.id}] 服务器解包得到命令码0x{command:02x}, 扩展码0x{extend:02x}不符合主动上报格式"
            )
            return False
        return True

    def recv_data(
        self, socket: socket.socket, default_chunk_size: int = 1024
    ) -> Optional[Tuple[bytes, Optional[bytes]]]:
        """
        通过循环接收数据块(chunk)的方式，从 socket 中准确接收一个完整的消息。

        Args:
            sock (socket.socket): 一个已连接的 socket 对象。
            chunk_size (int): 每次从 socket 缓冲区读取的最大字节数。

        Returns:
            bytes: 接收到的完整消息字节流，如果连接中断则返回 None。
        """
        # 接受头部
        header_buffer = bytearray()
        logger.info(
            f"[雷达{self.id if self.id else '(未注册)'}] 正从{self.address}处接受头部数据，目标长度为{ArcSAR.HEADER_SIZE}"
        )
        while len(header_buffer) < ArcSAR.HEADER_SIZE:
            remaining = ArcSAR.HEADER_SIZE - len(header_buffer)  # 剩余需要接收的字节数
            try:
                chunk = socket.recv(min(remaining, default_chunk_size))
            except (ConnectionResetError, BrokenPipeError):
                chunk = b""  # 对待连接错误如同连接正常关闭
            if not chunk:
                return None
            header_buffer.extend(chunk)  # 将接收到的数据追加到缓冲区

        logger.info(
            f"[雷达{self.id if self.id else '(未注册)'}] 成功在{self.address}接收到头部数据，长度为{len(header_buffer)}"
        )
        header_data = bytes(header_buffer)  # 将缓冲区转换为字节流
        logger.info(
            f"[雷达{self.id if self.id else '(未注册)'}] 头部数据为{ArcSAR.format_hex_pretty(header_data)}"
        )

        # 解析数据长度
        try:
            *_, data_len = struct.unpack(self.HEADER_FORMAT, header_data)
            logger.info(
                f"[雷达{self.id if self.id else '(未注册)'}] 从{self.address}解析得到的数据长度为{data_len}"
            )
        except struct.error:
            logger.error(
                f"[雷达{self.id if self.id else '(未注册)'}] 从{self.address}得到的头部是无效的，无法解析数据长度"
            )
            return None  # 无法解析头部，放弃此包

        # 接受剩下的数据
        payload_buffer = bytearray()
        logger.info(
            f"[雷达{self.id if self.id else '(未注册)'}] 开始从{self.address}处接受数据，目标长度为{data_len}"
        )
        while len(payload_buffer) < data_len:
            remaining = data_len - len(payload_buffer)  # 剩余需要接收的字节数
            chunk = socket.recv(min(remaining, default_chunk_size))
            if not chunk:
                return None
            payload_buffer.extend(chunk)  # 将接收到的数据追加到缓冲区

        logger.info(
            f"[雷达{self.id if self.id else '(未注册)'}] 成功在{self.address}接收到主要数据，长度为{len(payload_buffer)}"
        )
        payload_data = bytes(payload_buffer)  # 将缓冲区转换为字节流
        logger.info(
            f"[雷达{self.id if self.id else '(未注册)'}] 主要数据为{ArcSAR.format_hex_pretty(payload_data)}"
        )

        return header_data, payload_data

    @staticmethod
    def format_hex_pretty(
        data: bytes, line_length: int = 28, indent: str = "  "
    ) -> str:
        """
        将字节串格式化为美观的十六进制字符串，带换行和缩进。

        Args:
            data (bytes): 需要格式化的字节数据。
            line_length (int): 每行显示的字节数。
            indent (str): 每行前面的缩进字符串。

        Returns:
            str: 格式化后的多行字符串。
        """
        if not data:
            return "空"

        lines = []
        for i in range(0, len(data), line_length):
            # 1. 切出当前行的数据块
            chunk = data[i : i + line_length]

            # 2. 将数据块转换为带空格的十六进制字符串
            hex_part = chunk.hex(" ").upper()

            # 3. (可选) 添加ASCII字符表示，方便对照
            # ascii_part = "".join(chr(b) if 32 <= b < 127 else "." for b in chunk)
            # lines.append(
            #     f"{indent}{i:08x}:  {hex_part:<{line_length * 3 - 1}} |{ascii_part}|"
            # )

            # 4. 格式化当前行，使其对齐
            #    - {i:08x}: 当前行的起始偏移量，用8位十六进制表示
            #    - {hex_part:<{line_length * 3 - 1}}: 左对齐的十六进制部分。
            #      每个字节占3个字符（2个hex + 1个空格），最后减1去掉末尾空格。
            #    - {ascii_part}: ASCII部分
            lines.append(f"{indent}{i:08x}:  {hex_part:<{line_length * 3 - 1}}")

        # 在最前面加上换行符，使其在日志中与上一行分开，看起来更清晰
        return "\n" + "\n".join(lines)

    def register_or_update_online_status(self) -> bool:
        """
        处理雷达首次连接的初始化工作

        此方法执行以下操作：
        1. 检查雷达是否已在数据库中注册
        2. 如果是新雷达：
           - 在数据库中创建雷达基本信息
           - 创建必要的文件目录结构
           - 从Excel导入雷达配置信息到MongoDB
        3. 如果是已知雷达：
           - 更新在线状态

        Returns:
            str: 'successfully!' 表示初始化成功

        Raises:
            OSError: 创建目录失败时抛出
            pd.errors.EmptyDataError: Excel文件为空时抛出
            pymongo.errors.PyMongoError: MongoDB操作失败时抛出
        """
        try:  # 一个全新的雷达连接到服务器
            radar_doc = radar_collection.find_one({"ID": self.id})
            if not radar_doc:
                # 初始化 radar_collection 中的 "ID" 对应的文档
                logger.info(
                    f"[雷达{self.id}] 设备ID为{self.id}的雷达是新雷达，开始初始化对应的数据库"
                )

                radar_collection.insert_one(
                    {
                        "ID": self.id,
                        "name": f"Radar{self.id}",
                        "is_online": 1,
                        "is_work": 0,
                        "mission_ID": [
                            # {
                            #     "ID": None,
                            #     "name": None,
                            #     "coordinates": None,
                            #     "radar_zero_theta": None,
                            # }
                        ],
                        "radar_coordinates": [],
                        "scene": None,
                    }
                )

                # 创建文件目录结构 不知道在干嘛
                radar_file_path = os.path.join(BASE_FILE_PATH, self.id)
                for subdir in ["algorithm_file", "log_file", "work_data", "radar_file"]:
                    os.makedirs(os.path.join(radar_file_path, subdir), exist_ok=True)
                logger.info(
                    f"[雷达{self.id}] 为设备ID为{self.id}的雷达创建了必要的文件目录结构"
                )

                # 根据 id 创建数据库
                db_this_radar = client[self.id]

                # 从Excel导入配置数据
                excel_sheets = {
                    "雷达信息": "radar_information",
                    "场景参数": "scene_parameter",
                    "平台命令": "platform_command",
                }

                for sheet_name, collection_name in excel_sheets.items():
                    df = pd.read_excel("my_code/data.xlsx", sheet_name=sheet_name)
                    logger.debug(f"[雷达{self.id}] 成功从Excel读取了{sheet_name}的数据")
                    data = df.to_dict(orient="records")
                    logger.debug(
                        f"[雷达{self.id}] 成功将{sheet_name}的数据转换为字典\n{data}"
                    )
                    db_this_radar[collection_name].insert_many(data)

                logger.info(
                    f"[雷达{self.id}] 成功为设备ID为{self.id}的雷达创建了数据库"
                )
                logger.info(f"[雷达{self.id}] 注册成功")
                return True
            else:  # 一个已经注册过的雷达重新连接
                # 更新雷达在线状态
                logger.info(f"[雷达{self.id}] 已经在数据库中注册")
                radar_collection.update_one({"ID": self.id}, {"$set": {"is_online": 1}})
                logger.info(f"[雷达{self.id}] 重新上线，更新在线状态为1")
                return True
        except OSError as e:
            logger.error(f"[雷达{self.id}] 创建文件目录结构失败: {e}")
            raise
        except pd.errors.EmptyDataError as e:
            logger.error(f"[雷达{self.id}] 从Excel导入配置数据失败: {e}")
            raise
        except Exception as e:
            logger.error(f"[雷达{self.id}] 未知错误: {e}")
            raise

    def update_radar_imformation(self, payload_data):
        """
        更新雷达基本信息

        此方法从接收到的数据中解析雷达信息并更新到数据库。支持的数据类型包括：
        - charchar: 字符数组，每个字符用点号分隔
        - char: 单个字符
        - float32: 32位浮点数
        - float64: 64位浮点数
        - int16: 16位整数
        - int32: 32位整数

        Returns:
            str: 空字符串表示成功

        Raises:
            struct.error: 数据解包错误时抛出
            pymongo.errors.PyMongoError: 数据库操作错误时抛出
        """
        try:
            logger.info(f"[雷达{self.id}] 开始解析雷达的基本信息")
            db_this_radar = client[self.id]

            radar_information_collection = db_this_radar["radar_information"]

            # 数据类型到解析函数的映射
            type_parsers = {
                "charchar": lambda pos, size: f"v.{'.'.join(str(n) for n in struct.unpack('<' + 'B' * size, payload_data[pos:pos + size]))}",
                "char": lambda pos, size: chr(
                    struct.unpack("<B", payload_data[pos : pos + size])[0]
                ),
                "float32": lambda pos, size: struct.unpack(
                    "<f", payload_data[pos : pos + size]
                )[0],
                "float64": lambda pos, size: struct.unpack(
                    "<d", payload_data[pos : pos + size]
                )[0],
                "int16": lambda pos, size: struct.unpack(
                    "<h", payload_data[pos : pos + size]
                )[0],
                "int32": lambda pos, size: struct.unpack(
                    "<I", payload_data[pos : pos + size]
                )[0],
            }

            # 处理每个雷达信息文档
            logger.info(f"[雷达{self.id}] 开始更新雷达数据库中的基本信息")
            for doc in radar_information_collection.find():
                start_pos: int = doc.get("start_byte_position")
                byte_size: int = doc.get("all_byte")
                data_type: str = doc.get("data_type")

                if data_type not in type_parsers:
                    logger.warning(f"[雷达{self.id}] 未知数据类型 {data_type}")
                    continue

                try:
                    # 解析数据
                    parsed_data = type_parsers[data_type](start_pos, byte_size)

                    # 更新数据库
                    radar_information_collection.update_one(
                        {"start_byte_position": start_pos},
                        {"$set": {"data": parsed_data}},
                    )
                except struct.error as e:
                    logger.error(
                        f"[雷达{self.id}] 数据解析错误，从负载数据的第{start_pos}位开始发生位置: {str(e)}"
                    )
                    continue
            logger.info(f"[雷达{self.id}] 成功更新雷达{self.id}数据库中的基本信息")
        except Exception as e:
            logger.error(f"[雷达{self.id}] 无法解析雷达信息: {e}")
            raise

    def upload_image_data(self, payload_data):
        """
        处理雷达上传的图像数据并保存到数据库和文件系统

        此方法执行以下操作：
        1. 解析接收到的二进制数据，包括序列号、MD5校验、时间戳、任务ID等元数据
        2. 对图像数据进行MD5校验
        3. 解压缩图像数据并转换为幅度和相位两个numpy数组
        4. 将图像数据保存为TIFF格式，包含元数据
        5. 将图像信息存入MongoDB数据库

        :param payload_data: 从雷达接收到的图像数据
        :return: 空字符串表示成功

        :raise struct.error: 数据解包错误时抛出
        :raise snappy.UncompressError: 数据解压缩错误时抛出
        :raise OSError: 文件写入错误时抛出
        :raise pymongo.errors.PyMongoError: 数据库操作错误时抛出
        """
        # 解析头部数据
        logger.info(f"[雷达{self.id}] 开始解析上传的散射图像数据")
        meta_data_format = "< I 16s 2I fIf fIf IIf"
        meta_data_size = struct.calcsize(meta_data_format)
        try:
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                sca_im_size,
                img_max_amp,
            ) = struct.unpack(meta_data_format, payload_data[:meta_data_size])
        except struct.error as e:
            logger.error(f"[雷达{self.id}] 散射图像数据头部解析错误: {str(e)}")
            return

        # 校验MD5
        logger.info(f"[雷达{self.id}] 开始校验雷达上传的散射图像数据MD5")
        md5_check_pos = struct.calcsize("<I 16s")
        md5_calculated = hashlib.md5(payload_data[md5_check_pos:]).hexdigest()
        if md5 != md5_calculated:
            logger.error(
                f"[雷达{self.id}] 散射图像数据MD5校验失败，实际值为{md5}，计算值为{md5_calculated}"
            )
            return

        # 解压缩和处理图像数据
        try:
            logger.info(f"[雷达{self.id}] 开始解压雷达上传的散射图像数据")
            M, N = rng_num, ang_num
            img_data_bytes = cast(
                bytes, snappy.uncompress(payload_data[meta_data_size:])
            )

            magnitude_data = (
                np.frombuffer(img_data_bytes[: 2 * M * N], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )

            phase_data = (
                np.frombuffer(img_data_bytes[2 * M * N :], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )
            logger.info(f"[雷达{self.id}] 成功解压雷达上传的散射图像数据")
        except Exception as e:
            logger.error(f"[雷达{self.id}] 解压缩图像数据失败: {str(e)}")
            return

        # 准备文件路径和数据库
        base_path = os.path.join(
            BASE_FILE_PATH, self.id, "work_data", str(mission_id), "image_data"
        )

        file_paths = {
            "polar": os.path.join(base_path, f"polar_{seq}.tiff"),
            "polar_phase": os.path.join(base_path, f"phase_{seq}.tiff"),
            "polar_magntiude": os.path.join(base_path, f"magnitude_{seq}.tiff"),
            "cart": os.path.join(base_path, f"cart_{seq}.png"),
        }
        logger.info(
            f"[雷达{self.id}] 成功创建雷达上传散射图像数据的文件路径: {file_paths}"
        )

        # 保存图像数据
        logger.info(f"[雷达{self.id}] 开始保存雷达上传的散射图像数据至文件系统")
        radar_doc = cast(Dict[str, Any], radar_collection.find_one({"ID": self.id}))
        mission_list = radar_doc.get("mission_ID", [])
        mission_details = next(
            (m for m in mission_list if m["ID"] == str(mission_id)), None
        )

        meta2save = {
            "description": "雷达散射图像数据，第一张为幅度图，第二张为相位图",
            "theta0": ang_min,
            "dtheta": ang_res,
            "r0": rng_min,
            "dr": rng_res,
            "coordinates": (
                mission_details.get("coordinates", []) if mission_details else []
            ),
            "radar_zero_theta": (
                mission_details.get("radar_zero_theta", 0) if mission_details else 0
            ),
        }

        # 保存原始极坐标图像
        imwrite(
            file_paths["polar"],
            np.stack([magnitude_data, phase_data], axis=0),
            metadata=meta2save,
        )
        # 分别保存极坐标图像的幅度和相位
        imwrite(
            file_paths["polar_phase"],
            phase_data,
            metadata=meta2save,
        )
        imwrite(
            file_paths["polar_magntiude"],
            magnitude_data,
            metadata=meta2save,
        )
        logger.info(f"[雷达{self.id}] 成功保存雷达上传的散射图像数据至文件系统")

        # 把极坐标图像转换为笛卡尔坐标
        mp.Process(
            target=ArcSAR.polar2cart,
            args=(
                magnitude_data,
                rng_min,
                rng_res,
                rng_num,
                ang_min,
                ang_res,
                ang_num,
                file_paths["cart"],
                meta2save,
            ),
        ).start()

        # 保存到数据库，图片的元数据，图片的本地路径
        logger.info(f"[雷达{self.id}] 开始保存雷达上传的散射图像数据至数据库")
        db_this_radar = client[self.id]
        img_collection = db_this_radar[f"img_data_{mission_id}"]

        img_doc = {
            "扫描序号": seq,
            "时间戳": time_stamp,
            "任务ID": mission_id,
            "距离像素间隔": rng_res,
            "距离像素点数量": rng_num,
            "最小距离": rng_min,
            "角度像素间隔": ang_res,
            "角度像素点数量": ang_num,
            "最小角度": ang_min,
            "数据类型": data_type,
            "最大幅值": img_max_amp,
            "road_polar": file_paths["polar"],
            "road_cart": file_paths["cart"],
            "road_polar_phase": file_paths["polar_phase"],
            "road_polar_magntiude": file_paths["polar_magntiude"],
        }
        img_collection.insert_one(img_doc)
        logger.info(f"[雷达{self.id}] 成功保存雷达上传的散射图像数据至数据库")
        return

    def upload_defo_or_confi_data(self, payload_data):
        # 解析头部数据
        logger.info(f"[雷达{self.id}] 开始解析雷达上传的(形变 | 置信度)图像数据")
        meta_data_format = "< I 16s 2I fIf fIf II"
        meta_data_size = struct.calcsize(meta_data_format)
        try:
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                im_size,
            ) = struct.unpack(meta_data_format, payload_data[:meta_data_size])
        except struct.error as e:
            logger.error(
                f"[雷达{self.id}] (形变 | 置信度)图像数据头部解析错误: {str(e)}"
            )
            return

        if data_type == 20:
            img_type_en, img_type_cn = "deformation", "形变"
            logger.info(f"[雷达{self.id}] 雷达上传的图像类型为：形变图像")
        elif data_type == 30:
            img_type_en, img_type_cn = "Confidence", "置信度"
            logger.info(f"[雷达{self.id}] 雷达上传的图像类型为：置信度图像")
        else:
            logger.error(f"[雷达{self.id}] 未知的数据类型: {data_type}")
            return

        # 校验MD5
        logger.info(f"[雷达{self.id}] 开始校验雷达上传的{img_type_cn}图像数据MD5")
        md5_check_pos = struct.calcsize("<I 16s")
        md5_calculated = hashlib.md5(payload_data[md5_check_pos:]).hexdigest()
        if md5 != md5_calculated:
            logger.error(
                f"[雷达{self.id}] {img_type_cn}图像数据MD5校验失败，实际值为{md5}，计算值为{md5_calculated}"
            )
            return

        # 解压缩和处理图像数据
        try:
            logger.info(f"[雷达{self.id}] 开始解压雷达上传的{img_type_cn}图像数据")
            M, N = rng_num, ang_num
            img_data_bytes = cast(
                bytes, snappy.uncompress(payload_data[meta_data_size:])
            )

            img_data = (
                np.frombuffer(img_data_bytes[: 2 * M * N], dtype=np.float16)
                .reshape(N, M)
                .astype(np.float32)
            )
            logger.info(f"[雷达{self.id}] 成功解压雷达上传的{img_type_cn}图像数据")
        except Exception as e:
            logger.error(f"[雷达{self.id}] 解压缩图像数据失败: {str(e)}")
            return

        # 准备文件路径和数据库
        base_path = os.path.join(
            BASE_FILE_PATH, self.id, "work_data", str(mission_id), f"{img_type_en}_data"
        )

        file_path = os.path.join(base_path, f"{img_type_en}_img_{seq}.tiff")

        logger.info(
            f"[雷达{self.id}] 成功创建雷达上传的{img_type_cn}图像数据文件路径: {file_path}"
        )

        # 保存图像数据
        logger.info(
            f"[雷达{self.id}] 开始保存雷达上传的{img_type_cn}图像数据至文件系统"
        )
        radar_doc = cast(Dict[str, Any], radar_collection.find_one({"ID": self.id}))
        mission_list = radar_doc.get("mission_ID", [])
        mission_details = next(
            (m for m in mission_list if m["ID"] == str(mission_id)), None
        )

        meta2save = {
            "description": "雷达散射图像数据，第一张为幅度图，第二张为相位图",
            "theta0": ang_min,
            "dtheta": ang_res,
            "r0": rng_min,
            "dr": rng_res,
            "coordinates": (
                mission_details.get("coordinates", []) if mission_details else []
            ),
            "radar_zero_theta": (
                mission_details.get("radar_zero_theta", 0) if mission_details else 0
            ),
        }

        # 保存原始极坐标图像
        imwrite(
            file_path,
            img_data,
            metadata=meta2save,
        )
        logger.info(
            f"[雷达{self.id}] 成功保存雷达上传的{img_type_cn}图像数据至文件系统"
        )

        # 保存到数据库，图片的元数据，图片的本地路径
        logger.info(f"[雷达{self.id}] 开始保存雷达上传的{img_type_cn}图像数据至数据库")
        db_this_radar = client[self.id]
        img_collection = db_this_radar[f"{img_type_en.lower()}_data_{mission_id}"]

        img_doc = {
            "扫描序号": seq,
            "时间戳": time_stamp,
            "任务ID": mission_id,
            "距离像素间隔": rng_res,
            "距离像素点数量": rng_num,
            "最小距离": rng_min,
            "角度像素间隔": ang_res,
            "角度像素点数量": ang_num,
            "最小角度": ang_min,
            "数据类型": data_type,
            "图像大小": im_size,
            "road_path": file_path,
        }
        img_collection.insert_one(img_doc)
        logger.info(f"[雷达{self.id}] 成功保存雷达上传的{img_type_cn}图像数据至数据库")
        return

    def upload_moving_target_data(self, payload_data):
        # 解析头部数据
        logger.info(f"[雷达{self.id}] 开始解析雷达上传的动目标数据")
        meta_data_format = "< I 16s 2I fIf fIf III"
        meta_data_size = struct.calcsize(meta_data_format)
        try:
            (
                seq,
                md5,
                time_stamp,
                mission_id,
                rng_res,
                rng_num,
                rng_min,
                ang_res,
                ang_num,
                ang_min,
                data_type,
                mt_num,
                mt_data_size,
            ) = struct.unpack(meta_data_format, payload_data[:meta_data_size])
        except struct.error as e:
            logger.error(f"[雷达{self.id}] 动目标图像数据头部解析错误: {str(e)}")
            return

        # 校验MD5
        logger.info(f"[雷达{self.id}] 开始校验雷达上传的动目标数据MD5")
        md5_check_pos = struct.calcsize("<I 16s")
        md5_calculated = hashlib.md5(payload_data[md5_check_pos:]).hexdigest()
        if md5 != md5_calculated:
            logger.error(
                f"[雷达{self.id}] 动目标数据MD5校验失败，实际值为{md5}，计算值为{md5_calculated}"
            )
            return

        # 获取动目标数据
        mt_data_bytes = payload_data[meta_data_size : meta_data_size + mt_data_size]
        all_target = []
        for i in range(mt_num):
            target_format = "< 4f"
            target_size = struct.calcsize(target_format)
            target_data = struct.unpack(
                target_format, mt_data_bytes[i * target_size : (i + 1) * target_size]
            )
            all_target.append(
                {
                    "目标编号": target_data[0],
                    "目标角度": target_data[1],
                    "目标距离": target_data[2],
                    "目标速度": target_data[3],
                }
            )
        logger.info(f"[雷达{self.id}] 成功解析雷达上传的动目标数据")

        # 保存到数据库
        logger.info(f"[雷达{self.id}] 开始保存雷达上传的动目标数据至数据库")
        db_this_radar = client[self.id]

        img_doc = {
            "扫描序号": seq,
            "时间戳": time_stamp,
            "任务ID": mission_id,
            "距离像素间隔": rng_res,
            "距离像素点数量": rng_num,
            "最小距离": rng_min,
            "角度像素间隔": ang_res,
            "角度像素点数量": ang_num,
            "最小角度": ang_min,
            "数据类型": data_type,
            "动目标个数": mt_num,
        }
        img_doc["目标信息"] = all_target
        mt_collection = db_this_radar[f"move_target_data_{mission_id}"]
        mt_collection.insert_one(img_doc)
        logger.info(f"[雷达{self.id}] 成功保存雷达上传的动目标数据至数据库")
        return

    def upload_log_file(self, payload_data):
        name_str = payload_data[:128].decode("ascii").split(",")[0]
        log_road = os.path.join(BASE_FILE_PATH, self.id, "log_file")
        logger.info(f"[雷达{self.id}] 开始保存雷达上传的日志数据至文件系统")
        path = os.path.join(log_road, name_str)
        logger.info(f"[雷达{self.id}] 日志文件保存路径为{path}")
        with open(path, "ab") as f:
            f.write(payload_data[128:])
            print("[雷达{self.id}] 接收log文件成功")
        return

    def update_radar_time(self, _):
        time_stamp = int(time.time())
        sendback_data = struct.pack("<I", time_stamp)
        logger.info(
            f"[雷达{self.id}] 成功更新雷达的时间戳为{time_stamp}, 对应时间为{datetime.fromtimestamp(time_stamp)}"
        )
        return sendback_data

    @staticmethod
    def polar2cart(
        img,
        rng_min,
        rng_res,
        rng_num,
        ang_min,
        ang_res,
        ang_num,
        file_path,
    ):
        ang_values = np.linspace(ang_min, ang_min + ang_res * (ang_num - 1), ang_num)
        rng_values = np.linspace(rng_min, rng_min + rng_res * (rng_num - 1), rng_num)

        rr, aa = np.meshgrid(rng_values, ang_values)
        x = rr * np.cos(aa)
        y = rr * np.sin(aa)

        x_cart = np.linspace(x.min(), x.max(), rng_num)
        y_cart = np.linspace(y.min(), y.max(), ang_num)
        xx_new, yy_new = np.meshgrid(x_cart, y_cart)

        # 3. 插值计算
        # 将原始(x, y)坐标点展平，以符合griddata的输入格式 (N, 2)
        source_points = np.vstack((x.ravel(), y.ravel())).T

        # 将幅度数据插值到新的笛卡尔网格上
        img = griddata(
            points=source_points,
            values=img.ravel(),
            xi=(xx_new, yy_new),
            method="linear",  # 使用线性插值，'nearest'更快但效果粗糙，'cubic'更平滑但计算慢
            fill_value=0,  # 如果插值点没有对应的数据，则返回0
        )

        # 保存到文件
        img = 20 * np.log10(
            np.where(
                img == 0,  # 条件：如果值等于0
                np.min(img[img != 0]),  # 则替换为：数组中最小的非零值
                img,  # 否则：保持原值
            )
            / np.max(img)
        )

        plt.axis("off")
        min_val = np.min(img)
        alpha = np.ones_like(img)
        alpha[img == min_val] = 0
        plt.imshow(img, cmap="jet", aspect="auto", alpha=alpha)
        plt.savefig(file_path, bbox_inches="tight", pad_inches=0)

    def get_state(self, state_code):
        state = {
            0x00: "设置正确",
            0x01: "参数错误",
            0x02: "数据长度错误",
            0x03: "重发",
            0x04: "雷达正在加载参数,暂时不能开始工作",
            0x05: "处于工作状态,设置失败",
        }.get(state_code, "未知错误")
        return state

    def query_radar_state(self) -> str:
        """
        查询雷达状态 - 使用队列机制避免竞争条件
        """
        logger.info(f"[雷达{self.id}] 开始查询雷达的状态")
        self.counter += 1

        # 创建事件和响应容器
        response_event = Event()
        response_data: Dict[str, Any] = {}  # 使用列表来存储响应数据

        # 将counter和event存入pending_responses
        self.pending_responses[self.counter] = (response_event, response_data)

        # 创建指令并放入队列
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),  # 将字符串ID转换为整数
            0x02,
            0x00,
            0x00,
            self.counter,
            0x00,
            0,
        )
        self.command_queue.put(send_code)
        logger.info(f"[雷达{self.id}] 已将counter={self.counter}的状态查询指令放入队列")

        # 等待响应，设置超时
        if not response_event.wait(timeout=10):
            # 超时，清理pending_responses
            if self.counter in self.pending_responses:
                del self.pending_responses[self.counter]
            logger.error("[雷达{self.id}] 等待状态查询响应超时")
            return "等待响应超时"

        # 获取响应数据
        state_code = response_data.get("state_code")
        if state_code is None:
            logger.error("[雷达{self.id}] 工作控制命令响应数据的状态码为空")
            return "状态码为空"

        state = self.get_state(state_code)
        if state != "设置正确":
            logger.error(f"[雷达{self.id}] 雷达状态查询失败，状态为{state}")
            return f"[雷达{self.id}] 雷达状态查询失败，状态为{state}"

        logger.info(f"[雷达{self.id}] 雷达状态查询成功，状态为{state}")
        self.update_radar_imformation(response_data.get("data"))
        return "success"

    # 设置场景参数
    def set_scene_parameter(self, parameters: Dict[str, Any] = {}) -> str:
        """
        设置场景参数
        """
        logger.info(f"[雷达{self.id}] 开始设置雷达的场景参数")
        self.counter += 1

        db_this_radar = client[self.id]
        scene_parameter_collection = db_this_radar["scene_parameter"]

        # 使用传参的数据直接设置
        # 如果传参的参数为空，则使用数据库中的数据
        payload_data = None
        if parameters == {}:
            # 使用数据库的数据直接设置
            payload_data_buffer = bytearray()
            for scene_parameter_doc in scene_parameter_collection.find():
                start_byte_position = scene_parameter_doc.get("start_byte_position")
                all_byte_int = scene_parameter_doc.get("all_byte")
                data_type = scene_parameter_doc.get("data_type")
                temp_data = scene_parameter_doc.get("data")
                if data_type == "int16":
                    temp_data = struct.pack(
                        "<h",
                        temp_data,
                    )
                elif data_type == "int32":
                    temp_data = struct.pack(
                        "<I",
                        temp_data,
                    )
                elif data_type == "float32":
                    temp_data = struct.pack(
                        "<f",
                        temp_data,
                    )
                elif data_type == "float64":
                    temp_data = struct.pack(
                        "<d",
                        temp_data,
                    )
                payload_data_buffer[
                    start_byte_position : start_byte_position + all_byte_int
                ] = temp_data
            payload_data = bytes(payload_data_buffer)
        else:
            try:
                validated_params = SceneParameter.model_validate(parameters)
                logger.info(f"[雷达{self.id}] 雷达场景参数校验成功")
                # parameters = validated_params.model_dump()
            except ValidationError as e:
                logger.error(f"[雷达{self.id}] 雷达场景参数校验失败: {e}")
                return f"[雷达{self.id}] 雷达场景参数校验失败: {e}"
            payload_data = validated_params.pack_to_bytes()

        # 创建事件和响应容器
        response_event = Event()
        response_data: Dict[str, Any] = {}  # 使用列表来存储响应数据

        # 将counter和event存入pending_responses
        self.pending_responses[self.counter] = (response_event, response_data)

        # 创建指令并放入队列
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),  # 将字符串ID转换为整数
            0x00,
            0x00,
            0x00,
            self.counter,
            0x00,
            len(payload_data),
        )
        self.command_queue.put(send_code + payload_data)
        logger.info(
            f"[雷达{self.id}] 已将counter={self.counter}的场景参数设置指令放入队列"
        )

        # 等待响应，设置超时
        if not response_event.wait(timeout=10):
            # 超时，清理pending_responses
            if self.counter in self.pending_responses:
                del self.pending_responses[self.counter]
            logger.error(f"[雷达{self.id}] 等待场景参数设置响应超时")
            return "等待响应超时"

        # 获取响应数据
        state_code = response_data.get("state_code")
        if state_code is None:
            logger.error(f"[雷达{self.id}] 场景参数设置响应数据的状态码为空")
            return "状态码为空"

        state = self.get_state(state_code)

        if state == "设置正确":
            logger.info(f"[雷达{self.id}] 雷达场景参数设置成功，状态为{state}")
            return "success"
        else:
            logger.error(f"[雷达{self.id}] 雷达场景参数设置失败，状态为{state}")
            return f"[雷达{self.id}] 雷达场景参数设置失败，状态为{state}"

    # 查询场景参数
    def query_scene_parameter(self):
        logger.info(f"[雷达{self.id}] 开始查询雷达的场景参数")
        self.counter += 1
        db_this_radar = client[self.id]
        scene_parameter_collection = db_this_radar["scene_parameter"]

        # 创建事件和响应容器
        response_event = Event()
        response_data: Dict[str, Any] = {}  # 使用列表来存储响应数据

        # 将counter和event存入pending_responses
        self.pending_responses[self.counter] = (response_event, response_data)

        # 创建指令并放入队列
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),  # 将字符串ID转换为整数
            0x01,
            0x00,
            0x00,
            self.counter,
            0x00,
            0,
        )
        self.command_queue.put(send_code)
        logger.info(
            f"[雷达{self.id}] 已将counter={self.counter}的场景参数查询指令放入队列"
        )

        # 等待响应，设置超时
        if not response_event.wait(timeout=10):
            # 超时，清理pending_responses
            if self.counter in self.pending_responses:
                del self.pending_responses[self.counter]
            logger.error("[雷达{self.id}] 等待场景参数查询响应超时")
            return "等待响应超时"

        # 获取响应数据
        state_code = response_data.get("state_code")
        state = self.get_state(state_code)

        if state != "设置正确":
            logger.error(f"[雷达{self.id}] 雷达场景参数查询失败，状态为{state}")
            return state

        data = response_data.get("data")
        if data is None:
            logger.error("[雷达{self.id}] 响应数据为空")
            return "响应数据为空"

        for scene_parameter_doc in scene_parameter_collection.find():
            start_byte_position = scene_parameter_doc.get("start_byte_position")
            all_byte_int = scene_parameter_doc.get("all_byte")
            data_type = scene_parameter_doc.get("data_type")
            temp_data = ""
            if data_type == "int16":
                temp_data = struct.unpack(
                    "<h",
                    data[start_byte_position : start_byte_position + all_byte_int],
                )[0]
            elif data_type == "int32":
                temp_data = struct.unpack(
                    "<I",
                    data[start_byte_position : start_byte_position + all_byte_int],
                )[0]
            elif data_type == "float32":
                temp_data = struct.unpack(
                    "<f",
                    data[start_byte_position : start_byte_position + all_byte_int],
                )[0]
            elif data_type == "float64":
                temp_data = struct.unpack(
                    "<d",
                    data[start_byte_position : start_byte_position + all_byte_int],
                )[0]
            scene_parameter_collection.update_one(
                {"start_byte_position": start_byte_position},
                {"$set": {"data": temp_data}},
            )
        logger.info(f"[雷达{self.id}] 设备场景参数查询成功")
        return "success"

    # 雷达控制，发送指令
    # extend_code
    # 0：开始工作
    # 1：停止工作
    # 2：重启雷达
    def radar_work_control(self, extend_code) -> str:
        """
        雷达工作控制 - 使用队列机制避免竞争条件
        """
        work_status = {
            0x00: "开始工作",
            0x01: "停止工作",
            0x02: "重启雷达",
        }.get(extend_code, "扩展码错误")
        logger.info(f"[雷达{self.id}] 开始向雷达发送工作控制指令:{work_status}")
        if extend_code not in [0x00, 0x01, 0x02]:
            logger.error(f"[雷达{self.id}] 工作控制指令错误: {work_status}")
            return "工作控制指令错误"

        self.counter += 1

        # 创建事件和响应容器
        response_event = Event()
        response_data: Dict[str, Any] = {}

        # 将counter和event存入pending_responses
        self.pending_responses[self.counter] = (response_event, response_data)

        # 创建指令并放入队列
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),  # 将字符串ID转换为整数
            0x03,
            extend_code,
            0x00,
            self.counter,
            0x00,
            0,
        )
        self.command_queue.put(send_code)
        logger.info(
            f"[雷达{self.id}] 已将counter={self.counter}的工作控制指令放入队列:{work_status}"
        )

        # 等待响应，设置超时
        if not response_event.wait(timeout=10):
            # 超时，清理pending_responses
            if self.counter in self.pending_responses:
                del self.pending_responses[self.counter]
            logger.error("[雷达{self.id}] 等待工作控制响应超时")
            return "等待响应超时"

        # 获取响应数据
        state_code = response_data.get("state_code")
        if state_code is None:
            logger.error("[雷达{self.id}] 工作控制命令响应数据的状态码为空")
            return "状态码为空"

        state = self.get_state(state_code)

        if state != "设置正确":
            logger.error(f"[雷达{self.id}] 雷达工作控制失败，状态为{state}")
            return f"[雷达{self.id}] 雷达工作控制失败，状态为{state}"

        # 根据扩展码执行相应的数据库操作
        db_this_radar = client[self.id]
        scene_parameter_collection = db_this_radar["scene_parameter"]
        this_radar_state: Dict[str, Any] = cast(
            Dict[str, Any], radar_collection.find_one({"ID": self.id})
        )

        if extend_code == 0x00:
            mission_id_doc = scene_parameter_collection.find_one(
                {"code_name": "MissionID"}
            )
            mission_id_doc = cast(Dict[str, Any], mission_id_doc)
            mission_id = str(mission_id_doc["data"])
            if not radar_collection.find_one(
                {"ID": self.id, "mission_ID.ID": mission_id}
            ):
                # 任务命名为当前时间
                time_now_str = datetime.now().strftime("%Y-%m-%d %H:%M")
                now_coordinates = this_radar_state["radar_coordinates"]
                radar_zero_theta = scene_parameter_collection.find_one(
                    {"code_name": "RadarOri"}
                )
                radar_collection.update_one(
                    {"ID": self.id},
                    {
                        "$addToSet": {
                            "mission_ID": {
                                "ID": mission_id,
                                "name": time_now_str,
                                "coordinates": now_coordinates,
                                "radar_zero_theta": radar_zero_theta,
                            }
                        }
                    },
                )
            path_file = os.path.join(BASE_FILE_PATH, self.id, "work_data", mission_id)
            if not os.path.exists(path_file):
                os.makedirs(os.path.join(path_file, "image_data"), exist_ok=True)
                os.makedirs(os.path.join(path_file, "deformation_data"), exist_ok=True)
                os.makedirs(os.path.join(path_file, "Confidence_data"), exist_ok=True)
                logger.info(f"[雷达{self.id}] 成功创建雷达的工作数据文件夹")
            radar_collection.update_one({"ID": self.id}, {"$set": {"is_work": 1}})
            logger.info(f"[雷达{self.id}] 雷达开始工作")
            return "success"
        elif extend_code == 0x01:
            radar_collection.update_one({"ID": self.id}, {"$set": {"is_work": 0}})
            logger.info(f"[雷达{self.id}] 雷达停止工作")
        elif extend_code == 0x02:
            radar_collection.update_one({"ID": self.id}, {"$set": {"is_online": 1}})
            logger.info(f"[雷达{self.id}] 雷达重启")
        else:
            logger.error(f"[雷达{self.id}] 工作指令响应结果的扩展码错误: {extend_code}")
            return "工作指令响应结果的扩展码错误"

        return "success"

    # 大气校正，发送指令
    # extend_code
    # 0：大气校正关
    # 1：大气校正开
    def atmospheric_correction_control(self, extend_code) -> str:
        """
        大气校正控制 - 使用队列机制避免竞争条件
        """
        work_status = {
            0x00: "大气校正关",
            0x01: "大气校正开",
        }.get(extend_code, "扩展码错误")
        logger.info(f"[雷达{self.id}] 开始向雷达发送大气校正控制指令:{work_status}")
        if extend_code not in [0x00, 0x01]:
            logger.error(f"[雷达{self.id}] 大气校正控制指令错误: {work_status}")
            return "大气校正控制指令错误"

        self.counter += 1

        # 创建事件和响应容器
        response_event = Event()
        response_data: Dict[str, Any] = {}

        # 将counter和event存入pending_responses
        self.pending_responses[self.counter] = (response_event, response_data)

        # 创建指令并放入队列
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),  # 将字符串ID转换为整数
            0x05,
            extend_code,
            0x00,
            self.counter,
            0x00,
            0,
        )
        self.command_queue.put(send_code)
        logger.info(
            f"[雷达{self.id}] 已将counter={self.counter}的大气校正控制指令放入队列:{work_status}"
        )

        # 等待响应，设置超时
        if not response_event.wait(timeout=10):
            # 超时，清理pending_responses
            if self.counter in self.pending_responses:
                del self.pending_responses[self.counter]
            logger.error("[雷达{self.id}] 等待大气校正控制响应超时")
            return "等待响应超时"

        # 获取响应数据
        state_code = response_data.get("state_code")
        if state_code is None:
            logger.error("[雷达{self.id}] 大气校正控制命令响应数据的状态码为空")
            return "状态码为空"

        state = self.get_state(state_code)

        if state != "设置正确":
            logger.error(f"[雷达{self.id}] 大气校正控制失败，状态为{state}")
            return f"[雷达{self.id}] 大气校正控制失败，状态为{state}"

        if extend_code == 0x00:
            logger.info(f"[雷达{self.id}] 大气校正关控制成功")
        elif extend_code == 0x01:
            logger.info(f"[雷达{self.id}] 大气校正开控制成功")
        else:
            logger.error(
                f"[雷达{self.id}] 大气校正控制响应结果的扩展码错误: {extend_code}"
            )
            return "大气校正控制响应结果的扩展码错误"

        return "success"

    def query_log_file(self) -> str:
        """
        查询日志文件 - 使用队列机制避免竞争条件

        1. 查询日志文件
        2. 更新数据库
            a. 新日志
                i. 插入数据库
                ii. 创建文件夹
                iii. 设置up_to_date为0
            b. 旧日志
                i. 更新up_to_date为0，意义是查询后这些文件名重新等待更新
        """
        logger.info(f"[雷达{self.id}] 开始查询雷达的日志")
        self.counter += 1
        db_this_radar = client[self.id]
        file_address_collection = db_this_radar["file_address"]

        # 创建事件和响应容器
        response_event = Event()
        response_data: Dict[str, Any] = {}
        # 将counter和event存入pending_responses
        self.pending_responses[self.counter] = (response_event, response_data)

        # 创建指令并放入队列
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),  # 将字符串ID转换为整数
            0x08,
            0x00,
            0x00,
            self.counter,
            0x00,
            0,
        )
        self.command_queue.put(send_code)
        logger.info(f"[雷达{self.id}] 已将counter={self.counter}的日志查询指令放入队列")

        # 等待响应，设置超时
        if not response_event.wait(timeout=10):
            # 超时，清理pending_responses
            if self.counter in self.pending_responses:
                del self.pending_responses[self.counter]
            logger.error(f"[雷达{self.id}] 等待日志查询响应超时")
            return "等待响应超时"
        # 获取响应数据
        state_code = response_data.get("state_code")
        if state_code is None:
            logger.error(f"[雷达{self.id}] 日志查询响应数据的状态码为空")
            return "无状态码"

        state = self.get_state(state_code)
        if state == "设置正确":
            logger.info(f"[雷达{self.id}] 雷达日志查询成功")
        else:
            logger.error(f"[雷达{self.id}] 雷达日志查询失败，状态为{state}")
            return f"[雷达{self.id}] 雷达日志查询失败，状态为{state}"

        data = response_data.get("data")
        if data is None:
            logger.error(f"[雷达{self.id}] 响应数据为空")
            return "响应数据为空"
        all_log_str = data.decode("ascii")
        log_str = all_log_str.split(",")
        if len(log_str) <= 0:
            logger.info(f"[雷达{self.id}] 无新日志")
            return "无新日志"

        for each_log in log_str:
            if file_address_collection.find_one({"log_name": each_log}) is None:
                logger.info(f"[雷达{self.id}] 新日志{each_log}")
                new_log = {
                    "log_name": each_log,
                    "log_road": os.path.join(
                        BASE_FILE_PATH, self.id, "radar_file", each_log
                    ),
                    "up_to_date": 0,
                    "type": "log文件" if each_log.endswith(".log") else "文件夹",
                }
                file_address_collection.insert_one(new_log)
            else:
                logger.info(f"[雷达{self.id}] 日志{each_log}已存在")
                file_address_collection.update_one(
                    {"log_name": each_log}, {"$set": {"up_to_date": 0}}
                )
        logger.info(f"[雷达{self.id}] 雷达日志查询成功")
        return "success"

    def download_log_file(self, log_name: str) -> str:
        """
        下载日志文件 - 使用队列机制避免竞争条件

        1. 查询日志文件
        2. 更新数据库
            a. 旧日志 -----> 下载前要查询一下，为下载操作提供下载地址
                i. 更新up_to_date为1
        """
        logger.info(f"[雷达{self.id}] 开始下载雷达日志{log_name}")
        self.counter += 1
        db_this_radar = client[self.id]
        file_address_collection = db_this_radar["file_address"]

        # 创建事件和响应容器
        response_event = Event()
        response_data: Dict[str, Any] = {}
        # 将counter和event存入pending_responses
        self.pending_responses[self.counter] = (response_event, response_data)
        # 创建指令并放入队列
        send_code = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.PLATFORM.value,
            int(self.id, 16),  # 将字符串ID转换为整数
            0x08,
            0x01,
            0x00,
            self.counter,
            0x00,
            len(log_name),
        )
        self.command_queue.put(send_code + log_name.encode("ascii"))
        logger.info(f"[雷达{self.id}] 已将counter={self.counter}的日志下载指令放入队列")

        # 等待响应，设置超时
        if not response_event.wait(timeout=10):
            # 超时，清理pending_responses
            if self.counter in self.pending_responses:
                del self.pending_responses[self.counter]
            logger.error(f"[雷达{self.id}] 等待日志下载指令响应超时")
            return "等待响应超时"

        # 获取响应数据
        state_code = response_data.get("state_code")
        if state_code is None:
            logger.error(f"[雷达{self.id}] 日志下载指令响应数据的状态码为空")
            return "无状态码"

        state = self.get_state(state_code)
        if state == "设置正确":
            logger.info(f"[雷达{self.id}] 雷达日志下载指令设置成功")
        else:
            logger.error(f"[雷达{self.id}] 雷达日志下载指令设置失败，状态为{state}")
            return f"[雷达{self.id}] 雷达日志下载指令设置失败，状态为{state}"

        data = response_data.get("data")
        if data is None:
            logger.error(f"[雷达{self.id}] 响应数据为空")
            return "响应数据为空"

        """
        内容段1：文件名，字符串，固定长度128
                文件名不足128，以逗号分隔，后续字节填0
        内容段2：文件转成的字节流
        """
        # file_name = data[:128].decode("ascii").split(",")[0]
        file_name = data[:128].decode("ascii").split("\x00", 1)[0]
        log_mess = file_address_collection.find_one({"log_name": file_name})
        if log_mess is None:
            logger.error(
                f"[雷达{self.id}] 日志{file_name}的下载地址不存在，请进行日志查询操作{self.query_log_file.__name__}"
            )
            return "日志下载地址不存在"

        log_mess = cast(Dict[str, Any], log_mess)
        log_road = cast(str, log_mess.get("log_road"))
        with open(log_road, "wb") as f:
            f.write(data[128:])
            logger.info(f"[雷达{self.id}] 雷达日志下载成功")
        file_address_collection.update_one(
            {"log_name": file_name}, {"$set": {"up_to_date": 1}}
        )
        return "success"

    def radar_disconnect(self):
        if self.id:
            logger.info(f"[雷达{self.id}] 雷达已断开连接")
            radar_collection.update_one({"ID": self.id}, {"$set": {"is_online": 0}})
            # 清理所有待处理的响应
            for counter, (event, _) in self.pending_responses.items():
                event.set()  # 通知等待的线程连接已断开
            self.pending_responses.clear()
            self.socket.close()
        else:
            logger.info(f"[雷达{self.id}] 未连接雷达，无法断开连接")


def tcp_server():
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    server_socket.bind((HOST, PORT))
    server_socket.listen(10)
    server_socket.setblocking(False)  # select 模式下，主socket应为非阻塞

    logger.info("[雷达服务器] 雷达服务器已启动，监听端口{}:{}".format(HOST, PORT))

    sockets_list = [server_socket]

    while True:
        try:
            # 检查哪些socket有待发送的指令
            writable_sockets_map = {}
            for sock in sockets_list:
                if sock != server_socket and sock in ArcSAR_map:
                    radar_client = ArcSAR_map[sock]
                    if not radar_client.command_queue.empty():
                        writable_sockets_map[sock] = radar_client

            readable_sockets, writable_sockets, exceptional_sockets = select.select(
                sockets_list, list(writable_sockets_map.keys()), sockets_list, 1
            )

            # 处理可写socket（发送指令）
            for sock in writable_sockets:
                radar_client = writable_sockets_map[sock]
                try:
                    command_to_send = radar_client.command_queue.get_nowait()
                    sock.sendall(command_to_send)
                    logger.info(f"[雷达服务器] 成功向雷达{radar_client.id}发送指令")
                except Exception as e:
                    logger.error(
                        f"[雷达服务器] 发送指令到雷达{radar_client.id}时出错: {e}"
                    )

            # 处理可读socket（接收数据）
            for sock in readable_sockets:
                if sock == server_socket:
                    # 新连接
                    client_socket, client_address = server_socket.accept()
                    client_socket.setblocking(False)  # 新的客户端socket也设为非阻塞
                    sockets_list.append(client_socket)

                    # 创建ArcSAR实例并存入映射
                    ArcSAR_map[client_socket] = ArcSAR(client_socket, client_address)
                    logger.info("[雷达服务器] 来自{}的新连接".format(client_address))
                else:
                    # 已有连接发来数据
                    radar_client = ArcSAR_map.get(sock)
                    if radar_client is None:
                        logger.warning(
                            f"[雷达服务器] 从 {sock} 套接字获取的ArcSAR对象不是有效的"
                        )
                        continue

                    try:
                        # 这里是关键修改：统一在主循环中接收数据
                        package = radar_client.recv_data(sock)
                        if not package:
                            # 连接断开
                            sockets_list.remove(sock)
                            radar_client.radar_disconnect()
                            del ArcSAR_map[sock]
                            if radar_client.id in ArcSAR_id_map:
                                del ArcSAR_id_map[radar_client.id]
                            continue

                        header_data, payload_data = package

                        # 解析头部以判断是响应还是主动上报
                        try:
                            revc_data = struct.unpack(ArcSAR.HEADER_FORMAT, header_data)
                            revc_dict = {
                                "head": revc_data[0],
                                "radar_id": revc_data[1],
                                "command": revc_data[2],
                                "extend": revc_data[3],
                                "sign": revc_data[4],
                                "con_int": revc_data[5],
                                "state_code": revc_data[6],
                                "data_len": revc_data[7],
                                "data": payload_data,
                            }
                        except struct.error as e:
                            logger.error(f"[雷达服务器] 解析头部失败: {e}")
                            continue

                        # 判断是响应还是主动上报
                        if (
                            revc_dict["sign"] == 0x01  # 这是一个响应
                            and revc_dict["con_int"]
                            in radar_client.pending_responses  # 这个counter正在等待响应
                            and revc_dict["command"] != 0x40  # 不是主动雷达上报的数据
                        ):
                            response_event, response_data_container = (
                                radar_client.pending_responses.pop(revc_dict["con_int"])
                            )
                            # response_data_container = revc_dict  # 将结果放入容器
                            # --- 这是正确的做法 ---
                            # 将收到的响应数据更新到原始的字典容器中
                            response_data_container.update(revc_dict)
                            response_event.set()  # 通知等待的线程
                            logger.info(
                                f"[雷达服务器] 收到雷达{radar_client.id}的响应，counter={revc_dict['con_int']}"
                            )
                        elif revc_dict["command"] == 0x40 and revc_dict["sign"] == 0x00:
                            # 这是一个主动上报，交给handle_radar_report处理
                            radar_client.handle_radar_report(header_data, payload_data)
                        else:
                            logger.warning(
                                f"[雷达服务器] 收到雷达{radar_client.id}的数据，但无法识别其类型，头部信息为{revc_dict}"
                            )

                    except Exception as e:
                        logger.error("[雷达服务器] 处理数据时发生错误:{}".format(e))
                        sockets_list.remove(sock)
                        sock.close()
                        if sock in ArcSAR_map:
                            del ArcSAR_map[sock]
                        if radar_client and radar_client.id in ArcSAR_id_map:
                            del ArcSAR_id_map[radar_client.id]

            # 处理异常的socket
            for sock in exceptional_sockets:
                logger.warning(f"[雷达服务器] 处理异常情况")
                if sock in sockets_list:
                    sockets_list.remove(sock)
                if sock in ArcSAR_map:
                    radar_client = ArcSAR_map[sock]
                    del ArcSAR_map[sock]
                    if radar_client.id in ArcSAR_id_map:
                        del ArcSAR_id_map[radar_client.id]
                sock.close()

        except (ConnectionResetError, ConnectionAbortedError) as e:
            logger.error(f"[雷达服务器] 连接被强制关闭: {e}")
            break  # 连接被强制关闭时退出循环
        except Exception as e:
            logger.error(f"[雷达服务器] 主循环中发生严重错误: {e}")
            break  # 出现严重错误时退出循环

    server_socket.close()


def test_query_radar_state(radar_client: ArcSAR):
    """Tests the radar state query functionality."""
    logger.info("--- [TEST] RUNNING: Query Radar State ---")
    result = radar_client.query_radar_state()
    logger.info(f"状态查询结果: {result}")
    assert result == "success"
    logger.info("--- [TEST] PASSED: Query Radar State ---")


def test_work_control(radar_client: ArcSAR):
    """Tests the radar work control (start, stop, reboot)."""
    logger.info("--- [TEST] RUNNING: Work Control ---")

    # 测试开始工作
    logger.info("Testing: Start Work (0x00)")
    result_start = radar_client.radar_work_control(0x00)
    logger.info(f"开始工作结果: {result_start}")
    assert result_start == "success"
    time.sleep(0.5)

    # 测试停止工作
    logger.info("Testing: Stop Work (0x01)")
    result_stop = radar_client.radar_work_control(0x01)
    logger.info(f"停止工作结果: {result_stop}")
    assert result_stop == "success"
    time.sleep(0.5)

    # 测试重启雷达
    logger.info("Testing: Reboot (0x02)")
    result_reboot = radar_client.radar_work_control(0x02)
    logger.info(f"重启雷达结果: {result_reboot}")
    assert result_reboot == "success"

    logger.info("--- [TEST] PASSED: Work Control ---")


def test_scene_parameter_management(radar_client: ArcSAR):
    """Tests setting and querying scene parameters, including validation."""
    logger.info("--- [TEST] RUNNING: Scene Parameter Management ---")

    # 1. 测试设置有效的场景参数
    logger.info("Testing: Set Valid Scene Parameters")
    valid_params = {
        "RoRate": 30,
        "RotAngBgn": 0.0,
        "RotAngEnd": 180.5,
        "RangeMin": 10.0,
        "RangeMax": 5000.0,
        "RadarMode": 0,
        "PS_TD": 20,
        "StartStopTime": 5,
        "EndStopTime": 5,
        "ScatImageEn": 1,
        "ScatImageDec": 2,
        "DefoImageEn": 1,
        "DefoImageDec": 1,
        "MissionIDSwitch": 1,
        "MissionID": 12345,
        "AntBWAz": 2,
        "AntSteerVt": 5,
        "RadarLon": 116.39,
        "RadarLat": 39.91,
        "RadarHei": 50.0,
        "RadarOri": 90.0,
        "RadarArmLen": 0.5,
        "FilterType": 3.0,
        "LocaltionType": 0,
    }
    result_set_valid = radar_client.set_scene_parameter(valid_params)
    logger.info(f"设置有效参数结果: {result_set_valid}")
    assert result_set_valid == "success"
    time.sleep(0.5)

    # 2. 测试查询场景参数（验证刚才的设置）
    logger.info("Testing: Query Scene Parameters")
    result_query = radar_client.query_scene_parameter()
    logger.info(f"查询参数结果: {result_query}")
    assert result_query == "success"
    time.sleep(0.5)

    # 3. 测试设置无效的场景参数
    logger.info("Testing: Set Invalid Scene Parameters (should fail validation)")
    invalid_params = {
        "RoRate": 99,  # Invalid literal
        "RangeMin": 6000.0,  # Will fail model_validator
        "RangeMax": 5000.0,
    }
    result_set_invalid = radar_client.set_scene_parameter(invalid_params)
    logger.info(f"设置无效参数结果: {result_set_invalid}")
    assert "校验失败" in result_set_invalid

    logger.info("--- [TEST] PASSED: Scene Parameter Management ---")


def test_atmospheric_correction(radar_client: ArcSAR):
    """Tests the atmospheric correction control."""
    logger.info("--- [TEST] RUNNING: Atmospheric Correction Control ---")

    # 测试开启大气校正
    logger.info("Testing: Turn On (0x01)")
    result_on = radar_client.atmospheric_correction_control(0x01)
    logger.info(f"开启大气校正结果: {result_on}")
    assert result_on == "success"
    time.sleep(0.5)

    # 测试关闭大气校正
    logger.info("Testing: Turn Off (0x00)")
    result_off = radar_client.atmospheric_correction_control(0x00)
    logger.info(f"关闭大气校正结果: {result_off}")
    assert result_off == "success"

    logger.info("--- [TEST] PASSED: Atmospheric Correction Control ---")


def test_log_management(radar_client: ArcSAR):
    """Tests querying and downloading log files."""
    logger.info("--- [TEST] RUNNING: Log File Management ---")

    # 1. 查询日志文件列表
    logger.info("Testing: Query Log File List")
    result_query = radar_client.query_log_file()
    logger.info(f"查询日志列表结果: {result_query}")
    assert result_query == "success"
    time.sleep(0.5)

    # 2. 下载一个日志文件
    #    我们在模拟器中硬编码了 "radar_2024-06-18.log"
    log_to_download = "radar_2024-06-18.log"
    logger.info(f"Testing: Download Log File '{log_to_download}'")
    result_download = radar_client.download_log_file(log_to_download)
    logger.info(f"下载日志文件结果: {result_download}")
    assert result_download == "success"

    # 验证文件是否真的被创建和写入
    log_path = os.path.join(
        BASE_FILE_PATH, radar_client.id, "radar_file", log_to_download
    )
    assert os.path.exists(log_path)
    with open(log_path, "r") as f:
        content = f.read()
        assert "This is a fake log entry." in content
    logger.info(f"已验证文件 '{log_path}' 创建成功且内容正确。")

    logger.info("--- [TEST] PASSED: Log File Management ---")


def run_all_tests():
    """
    主测试运行器，按顺序执行所有测试用例。
    """
    time.sleep(2)  # 等待服务器和模拟器完成连接和注册

    if not ArcSAR_id_map:
        logger.error(
            "!!! CRITICAL TEST FAILURE: No radar connected. Aborting all tests. !!!"
        )
        return

    # 获取第一个连接的雷达客户端实例
    radar_id = list(ArcSAR_id_map.keys())[0]
    radar_client = ArcSAR_id_map[radar_id]
    logger.info(f"=== STARTING TEST SUITE FOR RADAR [{radar_id}] ===")

    try:
        test_query_radar_state(radar_client)
        time.sleep(1)
        test_work_control(radar_client)
        time.sleep(1)
        test_scene_parameter_management(radar_client)
        time.sleep(1)
        test_atmospheric_correction(radar_client)
        time.sleep(1)
        test_log_management(radar_client)
    except AssertionError as e:
        logger.error(
            f"!!! TEST FAILED: Assertion failed in one of the tests. !!!", exc_info=True
        )
    except Exception as e:
        logger.error(f"!!! UNHANDLED EXCEPTION DURING TESTS: {e} !!!", exc_info=True)

    logger.info(f"=== TEST SUITE FOR RADAR [{radar_id}] COMPLETED ===")


def simulate_radar(host, port):
    """
    这个函数模拟一个雷达连接到服务器。
    它会响应服务器发送的所有指令，使其能够通过完整的测试套件。
    """
    FAKE_RADAR_ID_INT = 0x001005AD
    FAKE_RADAR_ID_HEX = f"{FAKE_RADAR_ID_INT:08X}"
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

    try:
        sock.connect((host, port))
        logger.info(f"[模拟器] 已连接到服务器 {host}:{port}")

        # 1. 发送初始化注册包 (雷达授时)
        logger.info("[模拟器] 发送初始化注册包...")
        initial_packet = struct.pack(
            ArcSAR.HEADER_FORMAT,
            CommandHeader.RADAR.value,
            FAKE_RADAR_ID_INT,
            0x40,
            0x06,
            0x00,
            0,
            0x00,
            0,
        )
        sock.sendall(initial_packet)

        while True:
            header_data = sock.recv(ArcSAR.HEADER_SIZE)
            if not header_data:
                break

            # 解包来自服务器的指令
            (head, radar_id, command, extend, sign, con_int, state_code, data_len) = (
                struct.unpack(ArcSAR.HEADER_FORMAT, header_data)
            )

            payload_data = sock.recv(data_len) if data_len > 0 else b""

            # 如果是服务器对我们注册包的响应，记录并忽略
            if command == 0x40 and sign == 0x01:
                time_stamp = struct.unpack("<I", payload_data)[0]
                logger.info(
                    f"[模拟器] 收到服务器对注册包的响应，完成注册和授时。授时结果为{datetime.fromtimestamp(time_stamp)}"
                )
                continue

            logger.info(
                f"[模拟器] 收到指令: cmd=0x{command:02x}, ext=0x{extend:02x}, counter={con_int}"
            )

            # --- 根据指令构建不同的响应 ---
            response_payload = b""
            # 响应【查询场景参数】
            if command == 0x01 and extend == 0x00:
                logger.info("[模拟器] 构建【查询场景参数】的响应载荷...")
                # 创建一个有效的 SceneParameter 实例并打包
                valid_params_obj = SceneParameter.model_validate(
                    {
                        "RoRate": 30,
                        "RotAngBgn": 0.0,
                        "RotAngEnd": 180.5,
                        "RangeMin": 10.0,
                        "RangeMax": 5000.0,
                        "RadarMode": 0,
                        "PS_TD": 20,
                        "StartStopTime": 5,
                        "EndStopTime": 5,
                        "ScatImageEn": 1,
                        "ScatImageDec": 2,
                        "DefoImageEn": 1,
                        "DefoImageDec": 1,
                        "MissionIDSwitch": 1,
                        "MissionID": 12345,
                        "AntBWAz": 2,
                        "AntSteerVt": 5,
                        "RadarLon": 116.39,
                        "RadarLat": 39.91,
                        "RadarHei": 50.0,
                        "RadarOri": 90.0,
                        "RadarArmLen": 0.5,
                        "FilterType": 3.0,
                        "LocaltionType": 0,
                    }
                )
                response_payload = valid_params_obj.pack_to_bytes()
            # 响应【查询日志列表】
            elif command == 0x08 and extend == 0x00:
                logger.info("[模拟器] 构建【查询日志列表】的响应载荷...")
                log_list_str = "radar_2024-06-18.log,system_events.log,config.txt"
                response_payload = log_list_str.encode("ascii")
            # 响应【下载日志文件】
            elif command == 0x08 and extend == 0x01:
                log_name_requested = payload_data.decode("ascii")
                logger.info(
                    f"[模拟器] 构建 [下载日志文件: {log_name_requested}]的响应载荷..."
                )
                header = f"{log_name_requested}".ljust(128, "\x00").encode("ascii")
                content = f"Log entry for {log_name_requested}\nTimestamp: {datetime.now()}\nThis is a fake log entry.".encode(
                    "ascii"
                )
                response_payload = header + content

            # 其他所有指令，都只返回一个成功的头部，没有载荷

            # 构建响应头部
            response_header = struct.pack(
                ArcSAR.HEADER_FORMAT,
                CommandHeader.RADAR.value,
                FAKE_RADAR_ID_INT,
                command,
                extend,
                0x01,  # 标记为响应
                con_int,  # 关键：返回相同的counter
                0x00,  # 成功状态码
                len(response_payload),
            )

            # 发送完整响应
            sock.sendall(response_header + response_payload)
            logger.info(
                f"[模拟器] 已为 counter {con_int} 发送响应 (载荷长度: {len(response_payload)})"
            )

    except ConnectionRefusedError:
        logger.error("[模拟器] 连接被拒绝。服务器是否正在运行？")
    except Exception as e:
        logger.error(f"[模拟器] 发生错误: {e}", exc_info=True)
    finally:
        sock.close()
        logger.info("[模拟器] 连接已关闭。")


if __name__ == "__main__":
    import threading

    # 1. 在后台守护线程中启动TCP服务器
    server_thread = threading.Thread(target=tcp_server, daemon=True)
    server_thread.start()
    logger.info("服务器线程已启动。")
    time.sleep(1)

    # 2. 在另一个后台守护线程中启动增强版的雷达模拟器
    radar_thread = threading.Thread(
        target=simulate_radar, args=(HOST, PORT), daemon=True
    )
    radar_thread.start()
    logger.info("雷达模拟器线程已启动。")

    # 3. 运行完整的测试套件
    run_all_tests()

    logger.info("主线程结束。守护线程将随之退出。")
    time.sleep(1)
