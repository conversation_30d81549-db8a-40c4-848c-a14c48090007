"""
用户管理模块
提供用户认证、注册、信息管理等功能
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import check_password_hash
from pymongo import MongoClient
import bcrypt  # 哈希加密
import json
import logging
from shared_config import (
    get_mongo,
    get_radar_collection,
    get_scene_collection,
    get_user_collection,
)
from type import ApiResponse
from validator import *

# 配置日志
logger = logging.getLogger(__name__)

# 创建蓝图
user = Blueprint("user", __name__)


@user.route("/login", methods=["POST"])
@handle_api_exceptions(info="用户登录")
def web_login() -> ApiResponse:
    access_token = create_access_token(
        identity=json.dumps(
            {
                "username": "24216131",
                "role": "管理员",
            }
        )
    )
    return jsonify(access_token=access_token, username="admin", role="管理员"), 200

    data = request.json
    username = data.get("username")
    password = data.get("password")
    # 查询数据库，获取符合条件的文档
    user_data = get_user_collection().find_one({"username": username}).unwrap()
    if not user_data:
        return jsonify({"status": "error", "message": "用户不存在"}), 400

    elif not bcrypt.checkpw(password.encode(), user_data["password"].encode()):
        return jsonify({"status": "error", "message": "密码错误"}), 400
    access_token = create_access_token(
        identity=json.dumps(
            {
                "username": user_data["username"],
                "role": user_data["role"],
            }
        )
    )
    return jsonify(
        access_token=access_token,
        username=user_data["username"],
        role=user_data["role"],
    )


@user.route("/register", methods=["POST"])
@handle_api_exceptions(info="用户注册")
@handle_database_exceptions
@validate_request("username", "password")
def web_register(**kwargs) -> ApiResponse:
    username = kwargs["username"]
    password = kwargs["password"]
    salt = bcrypt.gensalt()
    password_hashed = bcrypt.hashpw(password.encode(), salt).decode()  # 进行哈希
    role = "用户"

    # 检查用户是否已经存在
    if get_user_collection().find_one({"username": username}).unwrap():
        return jsonify({"status": "error", "message": "用户已注册"}), 200
    else:
        get_user_collection().insert_one(
            {"username": username, "password": password_hashed, "role": role}
        ).unwrap()
        access_token = create_access_token(
            identity=json.dumps(
                {
                    "username": username,
                    "role": role,
                }
            )
        )
        return jsonify(access_token=access_token, username=username, role=role), 200


@user.route("/change_password", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="修改密码")
@handle_database_exceptions
@validate_request("username", "password")
def web_change_password(**kwargs) -> ApiResponse:
    username = kwargs["username"]
    password = kwargs["password"]
    salt = bcrypt.gensalt()
    password_hashed = bcrypt.hashpw(password.encode(), salt).decode()  # 进行哈希
    get_user_collection().update_one(
        {"username": username}, {"$set": {"password": password_hashed}}
    ).unwrap()
    return jsonify({"status": "success", "message": "密码修改成功"}), 200
