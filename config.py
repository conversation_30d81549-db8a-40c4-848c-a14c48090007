# config.py
import os
from datetime import timedelta
from dotenv import load_dotenv

load_dotenv()


class Config:
    SECRET_KEY = os.environ.get("SECRET_KEY") or "|-+$%^&"

    # --- JWT 配置 ---
    JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY", "super-secret")
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(days=1)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # --- 数据库配置 ---
    MONGO_DB_NAME = os.environ.get("MONGO_DB_NAME", "base_data")
    MONGO_URI_BASE = os.environ.get("MONGO_URI_BASE", "mongodb://localhost:27017/")
    MONGO_URI = f"{MONGO_URI_BASE}{MONGO_DB_NAME}"

    # --- 文件服务配置 ---
    DOWNLOAD_BASE_DIR = os.environ.get(
        "DOWNLOAD_BASE_DIR", r"E:/实验室/云平台/react_app/public/source"
    )

    # --- CORS 配置 ---
    # 默认允许所有源，所有常见方法和头部，这在开发时可能方便，但生产环境需要更严格的设置
    CORS_ORIGINS = "*"  # 或者 ['http://localhost:3000', 'http://127.0.0.1:3000']
    CORS_METHODS = ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"]
    CORS_HEADERS = [
        "Content-Type",
        "Authorization",
        "X-Requested-With",
        "Accept",
    ]  # 添加常用的头部
    CORS_EXPOSE_HEADERS = []  # 根据需要暴露的头部
    CORS_SUPPORTS_CREDENTIALS = True  # 如果前端需要发送 cookies
    CORS_MAX_AGE = None  # 默认不缓存 OPTIONS 请求，或设置 timedelta


class DevelopmentConfig(Config):
    DEBUG = True
    SECRET_KEY = "your_secret_key"
    JWT_SECRET_KEY = "super-secret"
    # 开发环境通常需要更宽松的 CORS 设置，例如允许来自本地开发服务器的请求
    CORS_ORIGINS = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:5173",
    ]  # 假设前端在这些端口


class TestingConfig(Config):
    TESTING = True
    DEBUG = True
    # ... 其他测试配置 ...
    # 测试时可能不需要复杂的 CORS，或者有特定需求
    CORS_ORIGINS = "*"  # 或者根据测试客户端的来源设置


class ProductionConfig(Config):
    DEBUG = False
    TESTING = False
    SECRET_KEY = os.environ.get("SECRET_KEY")
    JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY")

    # 生产环境 CORS 配置应该非常严格，只允许你的前端应用的域名
    CORS_ORIGINS = (
        os.environ.get("CORS_ALLOWED_ORIGINS", "").split(",")
        if os.environ.get("CORS_ALLOWED_ORIGINS")
        else []
    )
    # 例如 CORS_ALLOWED_ORIGINS="https://yourapp.com,https://www.yourapp.com"
    CORS_SUPPORTS_CREDENTIALS = True  # 如果你的生产前端需要

    if not SECRET_KEY:
        raise ValueError("生产环境中 SECRET_KEY 必须在环境变量中设置")
    if not JWT_SECRET_KEY:
        raise ValueError("生产环境中 JWT_SECRET_KEY 必须在环境变量中设置")
    if (
        not CORS_ORIGINS and os.environ.get("CORS_ALLOWED_ORIGINS") is not None
    ):  # 确保如果设置了环境变量但解析后为空列表，则警告或报错
        print(
            "警告: 生产环境中 CORS_ALLOWED_ORIGINS 已设置但解析为空，将不允许任何跨域请求。"
        )
    elif not CORS_ORIGINS:
        # 如果未设置环境变量，则可以有一个安全的默认值，或者干脆不允许跨域（取决于你的应用）
        # 这里我们假设如果没有明确设置，则不允许跨域（空列表）
        print("警告: 生产环境中 CORS_ALLOWED_ORIGINS 未设置，将不允许任何跨域请求。")


config_by_name = dict(
    development=DevelopmentConfig,
    testing=TestingConfig,
    production=ProductionConfig,
    default=DevelopmentConfig,
)

