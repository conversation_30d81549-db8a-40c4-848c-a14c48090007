{".class": "MypyFile", "_fullname": "my_code.web_code.radar_manage", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ApiResponse": {".class": "SymbolTableNode", "cross_ref": "type.ApiResponse", "kind": "Gdef"}, "Blueprint": {".class": "SymbolTableNode", "cross_ref": "flask.blueprints.Blueprint", "kind": "Gdef"}, "DatabaseHelper": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.DatabaseHelper", "kind": "Gdef"}, "ObjectId": {".class": "SymbolTableNode", "cross_ref": "bson.objectid.ObjectId", "kind": "Gdef"}, "RequestValidator": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.RequestValidator", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.radar_manage.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.radar_manage.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.radar_manage.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.radar_manage.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.radar_manage.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "my_code.web_code.radar_manage.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "base_file_path": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "my_code.web_code.radar_manage.base_file_path", "name": "base_file_path", "type": "builtins.str"}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_collection": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.get_collection", "kind": "Gdef"}, "handle_api_exceptions": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.handle_api_exceptions", "kind": "Gdef"}, "handle_database_exceptions": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.handle_database_exceptions", "kind": "Gdef"}, "http": {".class": "SymbolTableNode", "cross_ref": "http", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "jwt_required": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.view_decorators.jwt_required", "kind": "Gdef"}, "load_dotenv": {".class": "SymbolTableNode", "cross_ref": "dotenv.main.load_dotenv", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "app.logger", "kind": "Gdef"}, "mongo": {".class": "SymbolTableNode", "cross_ref": "app.mongo", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "radar_code": {".class": "SymbolTableNode", "cross_ref": "my_code.radar_code", "kind": "Gdef"}, "radar_collection": {".class": "SymbolTableNode", "cross_ref": "app.radar_collection", "kind": "Gdef"}, "radar_manage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "my_code.web_code.radar_manage.radar_manage", "name": "radar_manage", "type": "flask.blueprints.Blueprint"}}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "scene_collection": {".class": "SymbolTableNode", "cross_ref": "app.scene_collection", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "user_collection": {".class": "SymbolTableNode", "cross_ref": "app.user_collection", "kind": "Gdef"}, "validate": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.validate", "kind": "Gdef"}, "validate_request": {".class": "SymbolTableNode", "cross_ref": "validator.validator_framework.validate_request", "kind": "Gdef"}, "web_change_mission_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_change_mission_name", "name": "web_change_mission_name", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_change_mission_name", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_change_mission_name", "name": "web_change_mission_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "web_change_radar_coordinates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_change_radar_coordinates", "name": "web_change_radar_coordinates", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_change_radar_coordinates", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_change_radar_coordinates", "name": "web_change_radar_coordinates", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "web_change_radar_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_change_radar_name", "name": "web_change_radar_name", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_change_radar_name", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_change_radar_name", "name": "web_change_radar_name", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "web_change_radar_name", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "web_change_scene_coordinates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_change_scene_coordinates", "name": "web_change_scene_coordinates", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_change_scene_coordinates", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_change_scene_coordinates", "name": "web_change_scene_coordinates", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "web_change_scene_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_change_scene_name", "name": "web_change_scene_name", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_change_scene_name", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_change_scene_name", "name": "web_change_scene_name", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "web_change_scene_name", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "validator.validator_framework.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "web_delete_mission": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_delete_mission", "name": "web_delete_mission", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_delete_mission", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_delete_mission", "name": "web_delete_mission", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "web_delete_radar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_delete_radar", "name": "web_delete_radar", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "web_delete_radar", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "type.ApiResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_delete_radar", "name": "web_delete_radar", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}, "web_get_all_radar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "my_code.web_code.radar_manage.web_get_all_radar", "name": "web_get_all_radar", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "my_code.web_code.radar_manage.web_get_all_radar", "name": "web_get_all_radar", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}}}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\my_code\\web_code\\radar_manage.py"}