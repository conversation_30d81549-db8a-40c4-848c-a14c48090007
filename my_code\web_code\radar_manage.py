"""
雷达管理模块
提供雷达设备的添加、删除、修改、查询等功能
"""

from bson import ObjectId
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
import my_code.radar_code as radar_code
from typing import cast
import logging
from shared_config import (
    get_mongo,
    get_radar_collection,
    get_scene_collection,
    get_user_collection,
)
from type import ApiResponse
import shutil  # 用于删除文件夹
import os
from validator import *
from dotenv import load_dotenv
import http

# 配置日志
logger = logging.getLogger(__name__)

load_dotenv()

# 创建蓝图
radar_manage = Blueprint("radar_manage", __name__)

# 雷达数据路径
base_file_path = os.environ.get("BASE_FILE_PATH") or ""


# 所有雷达管理列表获取
@radar_manage.route("/getAllRadar", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="获取所有雷达管理列表")
@handle_database_exceptions
def web_get_all_radar():
    scene_doc = get_scene_collection().find({}, {}).unwrap()
    send_data = []
    for each_scene_doc in scene_doc:
        radar_id_list = each_scene_doc.get("radar_ID")
        radar_id_list = cast(list, radar_id_list)
        radar_list = []
        each_scene = {}
        for radar_id in radar_id_list:
            radar_each = (
                get_radar_collection().find_one({"ID": radar_id}).not_empty().unwrap()
            )
            radar_list.append(radar_each)
        each_scene["name"] = each_scene_doc.get("name")
        each_scene["radar"] = radar_list
        each_scene["ID"] = str(each_scene_doc["_id"])

        send_data.append(each_scene_doc)
    return (
        jsonify(
            {
                "status": "success",
                "message": "雷达管理列表加载成功",
                "data": send_data,
            }
        ),
        200,
    )


# 雷达重命名
@radar_manage.route("/change_radar_name", methods=["POST"])
@handle_api_exceptions(info="雷达重命名")
@handle_database_exceptions
@validate_request("radar_ID", "name")
def web_change_radar_name(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    new_name = kwargs["name"]
    # radar_ID="001005ad"
    # new_name="雷达1"
    get_radar_collection().update_one(
        query={"ID": radar_id},
        update={"$set": {"name": new_name}},
        error_if_none_matched=True,
    ).unwrap()
    return (
        jsonify({"status": "success", "message": "雷达重命名成功"}),
        200,
    )


# 雷达任务重命名
@radar_manage.route("/change_mission_name", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="雷达任务重命名")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID", "name")
def web_change_mission_name(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_id = kwargs["mission_ID"]
    new_name = kwargs["name"]
    get_radar_collection().find_one(
        {"ID": radar_id, "mission_ID.ID": mission_id}
    ).not_empty(error_message="修改失败,数据库中无法查找到对应数据").unwrap()

    get_radar_collection().update_one(
        query={"ID": radar_id, "mission_ID.ID": mission_id},  # 找到包含该任务 ID 的文档
        update={"$set": {"mission_ID.$.name": new_name}},  # 更新该任务的 name 字段
        error_if_none_matched=True,
    ).unwrap()
    return (
        jsonify({"status": "success", "message": "任务名称修改成功"}),
        200,
    )


# 场景重命名
@radar_manage.route("/change_scene_name", methods=["POST"])
@handle_api_exceptions(info="场景重命名")
@handle_database_exceptions
@validate_request("sceneID", "name")
def web_change_scene_name(**kwargs) -> ApiResponse:
    scene_id = kwargs["sceneID"]
    new_name = kwargs["name"]
    # radar_ID="001005ad"
    # new_name="雷达1"
    get_scene_collection().update_one(
        query={"_id": ObjectId(scene_id)},
        update={"$set": {"name": new_name}},
        error_if_none_matched=True,
    ).unwrap()
    return (
        jsonify({"status": "success", "message": "场景重命名成功"}),
        200,
    )


# 删除雷达任务
@radar_manage.route("delete_mission", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="删除雷达任务")
@handle_database_exceptions
@validate_request("radar_ID", "mission_ID")
def web_delete_mission(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    mission_id = kwargs["mission_ID"]
    get_radar_collection().update_one(
        query={"ID": radar_id},  # 找到包含该任务 ID 的文档
        update={"$pull": {"mission_ID": {"ID": mission_id}}},  # 更新该任务的 name 字段
        error_if_none_matched=True,
    ).unwrap()

    get_collection(get_mongo(), radar_id, f"img_data_{mission_id}").drop().unwrap()
    get_collection(
        get_mongo(), radar_id, f"confidence_data_{mission_id}"
    ).drop().unwrap()
    get_collection(
        get_mongo(), radar_id, f"deformation_data_{mission_id}"
    ).drop().unwrap()
    get_collection(get_mongo(), radar_id, f"monitor_area_{mission_id}").drop().unwrap()
    # 删除相关文件
    file_path = os.path.join(base_file_path, radar_id, "work_data", mission_id)
    if os.path.exists(file_path):
        shutil.rmtree(file_path)
    logger.info("删除雷达：" + radar_id + " 的任务ID：" + mission_id)
    return jsonify({"status": "success", "message": "删除成功"}), 200


# 删除雷达设备
@radar_manage.route("/delete_radar", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="删除雷达设备")
@handle_database_exceptions
@validate_request("radar_ID")
def web_delete_radar(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    # 删除数据库中雷达信息
    print("4")
    get_mongo().drop_database(radar_id)
    print("3")
    db_this_radar = (
        get_radar_collection().find_one({"ID": radar_id}).not_empty().unwrap()
    )
    print("5")
    scene_id = db_this_radar.get("scene")
    # 删除场景数据库中绑定的雷达信息
    print("1")
    get_scene_collection().update_one(
        query={"_id": ObjectId(scene_id)},
        update={"$pull": {"radar_ID": radar_id}},
        error_if_none_matched=True,
    )
    print("2")
    # 删除雷达数据库中对应信息
    get_radar_collection().delete_one({"ID": radar_id}).unwrap()
    # 删除用户绑定的雷达！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！未实现
    # 删除相关文件
    file_path = os.path.join(base_file_path, radar_id)  # 任务文件夹路径
    if os.path.exists(file_path):
        shutil.rmtree(file_path)
    logger.info("删除雷达：", radar_id)
    return jsonify({"status": "success", "message": "删除雷达成功"}), 200


# 场景坐标设置
@radar_manage.route("/change_scene_coordinates", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="场景坐标设置")
@handle_database_exceptions
@validate_request("scene_ID", "coordinates")
def web_change_scene_coordinates(**kwargs) -> ApiResponse:
    scene_id = kwargs["scene_ID"]
    coordinates = kwargs["coordinates"]
    coordinates[2] = 1500
    get_radar_collection().update_one(  # 奇怪！！！！！
        query={"_id": ObjectId(scene_id)},  # 找到包含该任务 ID 的文档
        update={"$set": {"coordinates": coordinates}},  # 更新该任务的 name 字段
        error_if_none_matched=True,
    ).unwrap()
    return (
        jsonify({"status": "success", "message": "场景坐标修改成功"}),
        200,
    )


# 雷达坐标设置
@radar_manage.route("/change_radar_coordinates", methods=["POST"])
@jwt_required()
@handle_api_exceptions(info="雷达坐标设置")
@handle_database_exceptions
@validate_request("radar_ID", "coordinates")
def web_change_radar_coordinates(**kwargs) -> ApiResponse:
    radar_id = kwargs["radar_ID"]
    coordinates = kwargs["coordinates"]
    get_radar_collection().update_one(
        {"ID": radar_id},  # 找到包含该任务 ID 的文档
        {"$set": {"radar_coordinates": coordinates}},  # 更新该任务的 name 字段
    ).unwrap()
    return (
        jsonify({"status": "success", "message": "雷达坐标修改成功"}),
        200,
    )
