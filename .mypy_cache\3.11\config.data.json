{".class": "MypyFile", "_fullname": "config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.Config", "name": "Config", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.Config", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.Config", "builtins.object"], "names": {".class": "SymbolTable", "CORS_EXPOSE_HEADERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "invalid_partial_type", "has_explicit_value"], "fullname": "config.Config.CORS_EXPOSE_HEADERS", "name": "CORS_EXPOSE_HEADERS", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "CORS_HEADERS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.CORS_HEADERS", "name": "CORS_HEADERS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "CORS_MAX_AGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.CORS_MAX_AGE", "name": "CORS_MAX_AGE", "type": {".class": "NoneType"}}}, "CORS_METHODS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.CORS_METHODS", "name": "CORS_METHODS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "CORS_ORIGINS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.Config.CORS_ORIGINS", "name": "CORS_ORIGINS", "type": "builtins.str"}}, "CORS_SUPPORTS_CREDENTIALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.Config.CORS_SUPPORTS_CREDENTIALS", "name": "CORS_SUPPORTS_CREDENTIALS", "type": "builtins.bool"}}, "DOWNLOAD_BASE_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.DOWNLOAD_BASE_DIR", "name": "DOWNLOAD_BASE_DIR", "type": "builtins.str"}}, "JWT_ACCESS_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.JWT_ACCESS_TOKEN_EXPIRES", "name": "JWT_ACCESS_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "JWT_REFRESH_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.JWT_REFRESH_TOKEN_EXPIRES", "name": "JWT_REFRESH_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "JWT_SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.JWT_SECRET_KEY", "name": "JWT_SECRET_KEY", "type": "builtins.str"}}, "MONGO_DB_NAME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.MONGO_DB_NAME", "name": "MONGO_DB_NAME", "type": "builtins.str"}}, "MONGO_URI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.MONGO_URI", "name": "MONGO_URI", "type": "builtins.str"}}, "MONGO_URI_BASE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.MONGO_URI_BASE", "name": "MONGO_URI_BASE", "type": "builtins.str"}}, "SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.Config.SECRET_KEY", "name": "SECRET_KEY", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.Config.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.Config", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DevelopmentConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.DevelopmentConfig", "name": "DevelopmentConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.DevelopmentConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.DevelopmentConfig", "config.Config", "builtins.object"], "names": {".class": "SymbolTable", "CORS_ORIGINS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.DevelopmentConfig.CORS_ORIGINS", "name": "CORS_ORIGINS", "type": null}}, "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.DevelopmentConfig.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "JWT_SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.DevelopmentConfig.JWT_SECRET_KEY", "name": "JWT_SECRET_KEY", "type": "builtins.str"}}, "SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.DevelopmentConfig.SECRET_KEY", "name": "SECRET_KEY", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.DevelopmentConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.DevelopmentConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProductionConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.ProductionConfig", "name": "ProductionConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.ProductionConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.ProductionConfig", "config.Config", "builtins.object"], "names": {".class": "SymbolTable", "CORS_ORIGINS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.CORS_ORIGINS", "name": "CORS_ORIGINS", "type": null}}, "CORS_SUPPORTS_CREDENTIALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.CORS_SUPPORTS_CREDENTIALS", "name": "CORS_SUPPORTS_CREDENTIALS", "type": "builtins.bool"}}, "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "JWT_SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.JWT_SECRET_KEY", "name": "JWT_SECRET_KEY", "type": null}}, "SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.SECRET_KEY", "name": "SECRET_KEY", "type": null}}, "TESTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.ProductionConfig.TESTING", "name": "TESTING", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.ProductionConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.ProductionConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestingConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "config.TestingConfig", "name": "TestingConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "config.TestingConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "config", "mro": ["config.TestingConfig", "config.Config", "builtins.object"], "names": {".class": "SymbolTable", "CORS_ORIGINS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "config.TestingConfig.CORS_ORIGINS", "name": "CORS_ORIGINS", "type": "builtins.str"}}, "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.TestingConfig.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "TESTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "config.TestingConfig.TESTING", "name": "TESTING", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "config.TestingConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "config.TestingConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "config_by_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "config.config_by_name", "name": "config_by_name", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["config.DevelopmentConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "load_dotenv": {".class": "SymbolTableNode", "cross_ref": "dotenv.main.load_dotenv", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\Documents\\Arcweb\\backend\\config.py"}